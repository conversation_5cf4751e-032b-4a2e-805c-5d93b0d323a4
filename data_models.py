"""
Data models and structures for FlashScore Parser
Contains all dataclasses, enums, and type definitions used across the parser modules.
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional


class SportType(Enum):
    """Supported sport types with their IDs"""
    FOOTBALL = 1
    TENNIS = 2
    BASKETBALL = 3
    HOCKEY = 4
    AMERICAN_FOOTBALL = 5
    BASEBALL = 6
    HANDBALL = 7
    VOLLEYBALL = 12
    BADMINTON = 21
    TABLE_TENNIS = 25


class MatchStatus(Enum):
    """Match status codes and their meanings"""
    FINISHED = '3'
    CANCELLED = '5'
    POSTPONED = '4'
    AFTER_PENALTIES = '11'
    AFTER_EXTRA_TIME = '10'
    TECHNICAL_DEFEAT = '54'
    NO_SHOW = '9'
    FIRST_HALF = '12'
    SECOND_HALF = '13'
    HALF_TIME = '38'
    AWAITING_UPDATES = '42'


@dataclass
class Team:
    """Team information"""
    name: str
    short_name: str
    image_url: str = ""


@dataclass
class Match:
    """Match information structure"""
    event_id: str
    start_time: datetime
    sport_id: int
    league_name: str
    country_id: int
    country_name: str
    home_team: Team
    away_team: Team
    status: str
    home_score: str = ""
    away_score: str = ""
    current_result: str = ""
    match_url: str = ""
    league_index: int = -1  # Position of league in API response (for whitelist filtering)
    tournament_hash: str = ""  # Tournament hash from ZC field (permanent ID)


@dataclass
class BettingOdds:
    """Betting odds structure"""
    home_win: float = 0.0
    draw: float = 0.0
    away_win: float = 0.0
    over_under_line: str = ""
    over_odds: float = 0.0
    under_odds: float = 0.0
    both_teams_score_yes: float = 0.0
    both_teams_score_no: float = 0.0


@dataclass
class HeadToHeadMatch:
    """Single head-to-head match data"""
    home_team: str
    away_team: str
    score: str
    result: str
    date: str = ""


@dataclass
class HeadToHeadData:
    """Complete head-to-head statistics"""
    overall_home: List[HeadToHeadMatch]
    overall_away: List[HeadToHeadMatch]
    head_to_head: List[HeadToHeadMatch]
    home_home: List[HeadToHeadMatch]
    away_away: List[HeadToHeadMatch]


@dataclass
class Lineup:
    """Match lineup information"""
    home_team: Dict[str, str]
    away_team: Dict[str, str]
    formations: Dict[str, str]
    substitutions: List[Dict[str, str]]


@dataclass
class MatchCommentary:
    """Match commentary events"""
    events: List[Dict[str, str]]
    timeline: List[Dict[str, str]]


@dataclass
class DetailedStats:
    """Detailed match statistics"""
    possession: Dict[str, float]
    shots: Dict[str, int]
    shots_on_target: Dict[str, int]
    corners: Dict[str, int]
    fouls: Dict[str, int]
    offsides: Dict[str, int]


@dataclass
class Standings:
    """Tournament standings"""
    teams: List[Dict[str, str]]
    groups: List[Dict[str, str]]  # For tournaments with groups


@dataclass
class MatchIncidents:
    """Match incidents (goals, cards, subs)"""
    goals: List[Dict[str, str]]
    cards: List[Dict[str, str]] 
    substitutions: List[Dict[str, str]]


@dataclass
class MatchStatistics:
    """Complete match statistics including all tabs data"""
    match: Match
    head_to_head: Optional[HeadToHeadData] = None
    betting_odds: Optional[BettingOdds] = None
    lineups: Optional[Lineup] = None
    commentary: Optional[MatchCommentary] = None
    statistics: Optional[DetailedStats] = None
    standings: Optional[Standings] = None
    incidents: Optional[MatchIncidents] = None
