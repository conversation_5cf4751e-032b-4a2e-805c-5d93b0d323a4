#!/usr/bin/env python3
"""
Пример использования нового парсера статистики матчей в формате FlashScore
"""

from flashscore_parser import FlashScoreParser
from data_models import MatchDetailedStatistics, StatisticStage, StatisticGroup, StatisticItem
import json

def example_parse_and_save_match_statistics():
    """
    Пример парсинга и сохранения статистики матча
    """
    
    print("📊 Пример использования парсера статистики FlashScore")
    print("=" * 60)
    
    # Создаем экземпляр парсера
    parser = FlashScoreParser()
    
    # Пример 1: Создание статистики вручную (для демонстрации формата)
    print("\n1️⃣ Создание примера статистики матча...")
    
    # Создаем статистику для основного времени
    match_stage = create_sample_match_stage()
    
    # Создаем статистику для первого тайма
    first_half_stage = create_sample_first_half_stage()
    
    # Создаем статистику для второго тайма
    second_half_stage = create_sample_second_half_stage()
    
    # Объединяем все стадии
    detailed_stats = MatchDetailedStatistics(
        data=[match_stage, first_half_stage, second_half_stage],
        last_change_key=None
    )
    
    print("✅ Создана детальная статистика с 3 стадиями")
    
    # Пример 2: Сохранение в формате FlashScore
    print("\n2️⃣ Сохранение статистики в формате FlashScore...")
    
    # Создаем фиктивный объект матча для демонстрации
    from data_models import Match, Team, MatchStatistics
    from datetime import datetime
    
    home_team = Team(name="Volta Redonda", short_name="VOL")
    away_team = Team(name="Avai", short_name="AVA")
    
    sample_match = Match(
        event_id="sample_match_123",
        start_time=datetime.now(),
        sport_id=1,
        league_name="Serie B Superbet",
        country_id=39,
        country_name="Brazil",
        home_team=home_team,
        away_team=away_team,
        status="FINISHED",
        current_result="1:0"
    )
    
    # Создаем объект MatchStatistics с нашей детальной статистикой
    match_stats = MatchStatistics(
        match=sample_match,
        detailed_statistics=detailed_stats
    )
    
    # Сохраняем в формате FlashScore
    success = parser.save_match_statistics_flashscore_format(
        match_stats, 
        output_dir="example_statistics"
    )
    
    if success:
        print("✅ Статистика сохранена в папку 'example_statistics'")
    else:
        print("❌ Ошибка при сохранении статистики")
    
    # Пример 3: Демонстрация JSON формата
    print("\n3️⃣ Пример JSON формата статистики:")
    print("-" * 40)
    
    from dataclasses import asdict
    
    # Конвертируем в формат FlashScore
    flashscore_format = convert_to_flashscore_format(detailed_stats)
    
    # Выводим первые несколько элементов для демонстрации
    sample_output = {
        "DATA": flashscore_format["DATA"][:1],  # Только первая стадия для краткости
        "LAST_CHANGE_KEY": flashscore_format["LAST_CHANGE_KEY"]
    }
    
    print(json.dumps(sample_output, indent=2, ensure_ascii=False))
    
    print("\n4️⃣ Доступные методы для работы со статистикой:")
    print("-" * 50)
    print("• parser.stats_parser.parse_detailed_statistics(response_text)")
    print("• parser.save_match_statistics_flashscore_format(match_stats, output_dir)")
    print("• parser.get_match_statistics(match) - получить полную статистику матча")
    
    print("\n✅ Пример завершен!")

def create_sample_match_stage():
    """Создает пример статистики для основного времени"""
    
    # Top stats группа
    top_stats_items = [
        StatisticItem("Expected Goals (xG)", "0.44", "0.98"),
        StatisticItem("Ball Possession", "65%", "35%"),
        StatisticItem("Total shots", "10", "10"),
        StatisticItem("Shots on target", "3", "3"),
        StatisticItem("Big Chances", "0", "2"),
        StatisticItem("Corner Kicks", "8", "3"),
        StatisticItem("Passes", "86% (479/557)", "75% (230/305)"),
        StatisticItem("Yellow Cards", "2", "3")
    ]
    
    # Shots группа
    shots_items = [
        StatisticItem("Expected Goals (xG)", "0.44", "0.98"),
        StatisticItem("xG on target (xGOT)", "0.64", "1.01"),
        StatisticItem("Total shots", "10", "10"),
        StatisticItem("Shots on target", "3", "3"),
        StatisticItem("Shots off target", "4", "2"),
        StatisticItem("Blocked Shots", "3", "5")
    ]
    
    # Attack группа
    attack_items = [
        StatisticItem("Big Chances", "0", "2"),
        StatisticItem("Corner Kicks", "8", "3"),
        StatisticItem("Touches in opposition box", "23", "19"),
        StatisticItem("Offsides", "1", "0")
    ]
    
    groups = [
        StatisticGroup("Top stats", top_stats_items),
        StatisticGroup("Shots", shots_items),
        StatisticGroup("Attack", attack_items)
    ]
    
    return StatisticStage("Match", groups)

def create_sample_first_half_stage():
    """Создает пример статистики для первого тайма"""
    
    top_stats_items = [
        StatisticItem("Expected Goals (xG)", "0.44", "0.60"),
        StatisticItem("Ball Possession", "56%", "44%"),
        StatisticItem("Total shots", "5", "5"),
        StatisticItem("Shots on target", "1", "2")
    ]
    
    groups = [
        StatisticGroup("Top stats", top_stats_items)
    ]
    
    return StatisticStage("1st Half", groups)

def create_sample_second_half_stage():
    """Создает пример статистики для второго тайма"""
    
    top_stats_items = [
        StatisticItem("Expected Goals (xG)", "0.00", "0.38"),
        StatisticItem("Ball Possession", "73%", "27%"),
        StatisticItem("Total shots", "5", "5"),
        StatisticItem("Shots on target", "2", "1")
    ]
    
    groups = [
        StatisticGroup("Top stats", top_stats_items)
    ]
    
    return StatisticStage("2nd Half", groups)

def convert_to_flashscore_format(detailed_stats: MatchDetailedStatistics) -> dict:
    """Конвертирует MatchDetailedStatistics в формат FlashScore"""
    
    from dataclasses import asdict
    
    stats_dict = asdict(detailed_stats)
    
    flashscore_format = {
        "DATA": []
    }
    
    for stage_data in stats_dict['data']:
        stage_formatted = {
            "STAGE_NAME": stage_data['stage_name'],
            "GROUPS": []
        }
        
        for group_data in stage_data['groups']:
            group_formatted = {
                "GROUP_LABEL": group_data['group_label'],
                "ITEMS": []
            }
            
            for item_data in group_data['items']:
                item_formatted = {
                    "INCIDENT_NAME": item_data['incident_name'],
                    "VALUE_HOME": item_data['value_home'],
                    "VALUE_AWAY": item_data['value_away']
                }
                group_formatted["ITEMS"].append(item_formatted)
            
            stage_formatted["GROUPS"].append(group_formatted)
        
        flashscore_format["DATA"].append(stage_formatted)
    
    flashscore_format["LAST_CHANGE_KEY"] = stats_dict['last_change_key']
    
    return flashscore_format

if __name__ == "__main__":
    example_parse_and_save_match_statistics()
