"""
Modern FlashScore Parser
A refactored and improved version of the original parser with better structure,
error handling, and API integration capabilities.

This file now serves as a compatibility layer and imports from the new modular structure.
"""

# Import all classes and functions from the new modular structure
from data_models import *
from utils import logger
from match_parser import MatchParser
from h2h_parser import HeadToHeadParser
from stats_parsers import (
    LineupParser, CommentaryParser, StatsParser, 
    StandingsParser, IncidentsParser, OddsParser
)
from main_parser import FlashScoreParser

# Import whitelist configuration for backward compatibility
from league_whitelist_config import (
    LeagueWhitelistManager,
    WhitelistMode,
    default_whitelist_manager,
    is_league_whitelisted
)

# All classes are now imported from the new modular structure above
# This file maintains backward compatibility for existing code that imports from flashscore_parser

# For convenience, expose the main parser class directly
__all__ = [
    'FlashScoreParser',
    'Match', 'Team', 'MatchStatistics', 'HeadToHeadData',
    'BettingOdds', 'Lineup', 'MatchCommentary', 'DetailedStats',
    'MatchDetailedStatistics', 'StatisticStage', 'StatisticGroup', 'StatisticItem',
    'Standings', 'MatchIncidents', 'SportType', 'MatchStatus',
    'MatchParser', 'HeadToHeadParser', 'LineupParser', 'CommentaryParser',
    'StatsParser', 'StandingsParser', 'IncidentsParser', 'OddsParser',
    'LeagueWhitelistManager', 'WhitelistMode', 'default_whitelist_manager',
    'is_league_whitelisted', 'logger'
]
