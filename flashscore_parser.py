"""
Modern FlashScore Parser
A refactored and improved version of the original parser with better structure,
error handling, and API integration capabilities.
"""

import requests
import time
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum

# Import whitelist configuration
from league_whitelist_config import (
    LeagueWhitelistManager,
    WhitelistMode,
    default_whitelist_manager,
    is_league_whitelisted
)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('flashscore_parser.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SportType(Enum):
    """Supported sport types with their IDs"""
    FOOTBALL = 1
    TENNIS = 2
    BASKETBALL = 3
    HOCKEY = 4
    AMERICAN_FOOTBALL = 5
    BASEBALL = 6
    HANDBALL = 7
    VOLLEYBALL = 12
    BADMINTON = 21
    TABLE_TENNIS = 25


class MatchStatus(Enum):
    """Match status codes and their meanings"""
    FINISHED = '3'
    CANCELLED = '5'
    POSTPONED = '4'
    AFTER_PENALTIES = '11'
    AFTER_EXTRA_TIME = '10'
    TECHNICAL_DEFEAT = '54'
    NO_SHOW = '9'
    FIRST_HALF = '12'
    SECOND_HALF = '13'
    HALF_TIME = '38'
    AWAITING_UPDATES = '42'


@dataclass
class Team:
    """Team information"""
    name: str
    short_name: str
    image_url: str = ""


@dataclass
class Match:
    """Match information structure"""
    event_id: str
    start_time: datetime
    sport_id: int
    league_name: str
    country_id: int
    country_name: str
    home_team: Team
    away_team: Team
    status: str
    home_score: str = ""
    away_score: str = ""
    current_result: str = ""
    match_url: str = ""
    league_index: int = -1  # Position of league in API response (for whitelist filtering)
    tournament_hash: str = ""  # Tournament hash from ZC field (permanent ID)


@dataclass
class BettingOdds:
    """Betting odds structure"""
    home_win: float = 0.0
    draw: float = 0.0
    away_win: float = 0.0
    over_under_line: str = ""
    over_odds: float = 0.0
    under_odds: float = 0.0
    both_teams_score_yes: float = 0.0
    both_teams_score_no: float = 0.0


@dataclass
class HeadToHeadMatch:
    """Single head-to-head match data"""
    home_team: str
    away_team: str
    score: str
    result: str
    date: str = ""


@dataclass
class HeadToHeadData:
    """Complete head-to-head statistics"""
    overall_home: List[HeadToHeadMatch]
    overall_away: List[HeadToHeadMatch]
    head_to_head: List[HeadToHeadMatch]
    home_home: List[HeadToHeadMatch]
    away_away: List[HeadToHeadMatch]


@dataclass
class Lineup:
    """Match lineup information"""
    home_team: Dict[str, str]
    away_team: Dict[str, str]
    formations: Dict[str, str]
    substitutions: List[Dict[str, str]]

@dataclass
class MatchCommentary:
    """Match commentary events"""
    events: List[Dict[str, str]]
    timeline: List[Dict[str, str]]

@dataclass
class DetailedStats:
    """Detailed match statistics"""
    possession: Dict[str, float]
    shots: Dict[str, int]
    shots_on_target: Dict[str, int]
    corners: Dict[str, int]
    fouls: Dict[str, int]
    offsides: Dict[str, int]

@dataclass
class Standings:
    """Tournament standings"""
    teams: List[Dict[str, str]]
    groups: List[Dict[str, str]]  # For tournaments with groups

@dataclass
class MatchIncidents:
    """Match incidents (goals, cards, subs)"""
    goals: List[Dict[str, str]]
    cards: List[Dict[str, str]] 
    substitutions: List[Dict[str, str]]

@dataclass
class MatchStatistics:
    """Complete match statistics including all tabs data"""
    match: Match
    head_to_head: Optional[HeadToHeadData] = None
    betting_odds: Optional[BettingOdds] = None
    lineups: Optional[Lineup] = None
    commentary: Optional[MatchCommentary] = None
    statistics: Optional[DetailedStats] = None
    standings: Optional[Standings] = None
    incidents: Optional[MatchIncidents] = None


from flashscore_api import FlashScoreAPI


class MatchParser:
    """Parser for match data from FlashScore API responses"""
    
    def __init__(self):
        self.status_mapping = {
            '3': 'FINISHED',
            '5': 'CANCELLED', 
            '4': 'POSTPONED',
            '11': 'AFTER_PENALTIES',
            '10': 'AFTER_EXTRA_TIME',
            '54': 'TECHNICAL_DEFEAT',
            '9': 'NO_SHOW'
        }
    
    def parse_matches_from_response(self, response_text: str, sport_id: int) -> List[Match]:
        """
        Parse matches from API response text
        
        Args:
            response_text: Raw API response
            sport_id: Sport type ID
            
        Returns:
            List of parsed Match objects
        """
        matches = []
        
        try:
            # Split response by league sections
            league_sections = response_text.split('~ZA÷')

            for league_index, section in enumerate(league_sections[1:]):  # Skip first empty section
                try:
                    # Extract league info
                    country_id = int(section.split('ZB÷')[1].split('¬')[0])
                    league_name = section.split('¬')[0]

                    # Extract tournament hash (ZC field)
                    tournament_hash = ""
                    lines = section.split('¬')
                    for line in lines:
                        if line.startswith('ZC÷'):
                            tournament_hash = line[3:]
                            break

                    # Extract individual matches
                    match_sections = section.split('~AA÷')

                    for match_section in match_sections[1:]:  # Skip first section
                        match = self._parse_single_match(match_section, sport_id, league_name, country_id, league_index, tournament_hash)
                        if match:
                            matches.append(match)

                except Exception as e:
                    logger.warning(f"Error parsing league section: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error parsing matches response: {e}")
            
        return matches
    
    def _parse_single_match(self, match_text: str, sport_id: int, league_name: str, country_id: int, league_index: int, tournament_hash: str) -> Optional[Match]:
        """Parse a single match from text data"""
        try:
            # Extract basic match info
            event_id = match_text.split('¬')[0]
            status = match_text.split('AC÷')[1].split('¬')[0]
            
            # Parse timestamp
            timestamp = int(match_text.split('AD÷')[1].split('¬')[0])
            start_time = datetime.fromtimestamp(timestamp)
            
            # Extract team names
            home_team_name = match_text.split('AE÷')[1].split('¬')[0]
            away_team_name = match_text.split('AF÷')[1].split('¬')[0]
            
            # Extract short names
            home_short = match_text.split('WM÷')[1].split('¬')[0]
            away_short = match_text.split('WN÷')[1].split('¬')[0]
            
            # Extract scores (if available)
            home_score = ""
            away_score = ""
            try:
                home_score = match_text.split('AG÷')[1].split('¬')[0]
                away_score = match_text.split('AH÷')[1].split('¬')[0]
            except:
                pass
            
            # Create team objects
            home_team = Team(name=home_team_name, short_name=home_short)
            away_team = Team(name=away_team_name, short_name=away_short)
            
            # Create match URL
            match_url = f'https://www.flashscore.com/match/{event_id}/#/match-summary'
            
            # Create match object
            match = Match(
                event_id=event_id,
                start_time=start_time,
                sport_id=sport_id,
                league_name=league_name,
                country_id=country_id,
                country_name="",  # Will be filled later if needed
                home_team=home_team,
                away_team=away_team,
                status=self.status_mapping.get(status, status),
                home_score=home_score,
                away_score=away_score,
                current_result=f"{home_score}:{away_score}" if home_score and away_score else "",
                match_url=match_url,
                league_index=league_index,  # Position of league in API response
                tournament_hash=tournament_hash  # Tournament hash from ZC field
            )
            
            return match
            
        except Exception as e:
            logger.warning(f"Error parsing single match: {e}")
            return None


class LineupParser:
    """Parser for match lineups from FlashScore API"""
    
    def parse_lineups(self, response_text: str) -> Optional[Lineup]:
        """
        Parse lineups data from API response
        
        Args:
            response_text: Raw lineups API response
            
        Returns:
            Lineup object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            # Initialize empty lineups
            home_players = {}
            away_players = {}
            formations = {'home': '', 'away': ''}
            substitutions = []

            # Parse home team lineup (with error checking)
            if '~LA÷' in response_text:
                sections = response_text.split('~LA÷')
                if len(sections) > 1:
                    home_section = sections[1].split('~LB÷')[0] if '~LB÷' in sections[1] else sections[1]
                    for player in home_section.split('¬'):
                        if 'LC÷' in player and 'LD÷' in player:
                            try:
                                player_id = player.split('LC÷')[1].split('¬')[0]
                                player_name = player.split('LD÷')[1].split('¬')[0]
                                home_players[player_id] = player_name
                            except IndexError:
                                continue
            
            # Parse away team lineup (with error checking)
            if '~LB÷' in response_text:
                sections = response_text.split('~LB÷')
                if len(sections) > 1:
                    away_section = sections[1].split('~LC÷')[0] if '~LC÷' in sections[1] else sections[1]
                    for player in away_section.split('¬'):
                        if 'LC÷' in player and 'LD÷' in player:
                            try:
                                player_id = player.split('LC÷')[1].split('¬')[0]
                                player_name = player.split('LD÷')[1].split('¬')[0]
                                away_players[player_id] = player_name
                            except IndexError:
                                continue
            
            # Parse formations (with error checking)
            if '~LE÷' in response_text:
                try:
                    formations['home'] = response_text.split('~LE÷')[1].split('¬')[0]
                except IndexError:
                    pass
            if '~LF÷' in response_text:
                try:
                    formations['away'] = response_text.split('~LF÷')[1].split('¬')[0]
                except IndexError:
                    pass
            
            # Parse substitutions (if any, with error checking)
            if '~LG÷' in response_text:
                subs_section = response_text.split('~LG÷')[1]
                for sub in subs_section.split('¬'):
                    try:
                        if 'LH÷' in sub and 'LI÷' in sub and 'LJ÷' in sub:
                            sub_data = {
                                'team': 'home' if 'LH÷' in sub else 'away',
                                'player_in': sub.split('LH÷')[1].split('¬')[0],
                                'player_out': sub.split('LI÷')[1].split('¬')[0],
                                'minute': sub.split('LJ÷')[1].split('¬')[0]
                            }
                            substitutions.append(sub_data)
                    except IndexError:
                        continue
            
            return Lineup(
                home_team=home_players,
                away_team=away_players,
                formations=formations,
                substitutions=substitutions
            )
            
        except Exception as e:
            logger.error(f"Error parsing lineups: {e}")
            return None


class CommentaryParser:
    """Parser for match commentary from FlashScore API"""
    
    def parse_commentary(self, response_text: str) -> Optional[MatchCommentary]:
        """
        Parse commentary data from API response
        
        Args:
            response_text: Raw commentary API response
            
        Returns:
            MatchCommentary object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            events = []
            timeline = []
            
            # Split into individual events
            event_sections = response_text.split('~MA÷')
            
            for section in event_sections[1:]:  # Skip first empty section
                try:
                    event_type = section.split('MB÷')[1].split('¬')[0]
                    minute = section.split('MC÷')[1].split('¬')[0]
                    text = section.split('MD÷')[1].split('¬')[0]
                    
                    event = {
                        'type': event_type,
                        'minute': minute,
                        'text': text,
                        'is_important': 'ME÷1' in section  # Important events have ME÷1 flag
                    }
                    
                    events.append(event)
                    
                    # Add to timeline if it's a major event
                    if event_type in ['GOAL', 'CARD', 'SUBSTITUTION', 'PENALTY']:
                        timeline.append({
                            'minute': minute,
                            'type': event_type,
                            'text': text
                        })
                        
                except Exception as e:
                    logger.debug(f"Error parsing commentary event: {e}")
                    continue
                    
            return MatchCommentary(
                events=events,
                timeline=timeline
            )
            
        except Exception as e:
            logger.error(f"Error parsing commentary: {e}")
            return None


class StatsParser:
    """Parser for detailed match statistics from FlashScore API"""
    
    def parse_stats(self, response_text: str) -> Optional[DetailedStats]:
        """
        Parse detailed statistics from API response
        
        Args:
            response_text: Raw statistics API response
            
        Returns:
            DetailedStats object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            stats = {
                'possession': {'home': 0, 'away': 0},
                'shots': {'home': 0, 'away': 0},
                'shots_on_target': {'home': 0, 'away': 0},
                'corners': {'home': 0, 'away': 0},
                'fouls': {'home': 0, 'away': 0},
                'offsides': {'home': 0, 'away': 0}
            }
            
            # Parse possession
            if '~SA÷' in response_text:
                home_poss = float(response_text.split('~SA÷')[1].split('¬')[0])
                stats['possession'] = {
                    'home': home_poss,
                    'away': 100 - home_poss
                }
            
            # Parse shots
            if '~SB÷' in response_text:
                home_shots = int(response_text.split('~SB÷')[1].split('¬')[0])
                away_shots = int(response_text.split('~SC÷')[1].split('¬')[0])
                stats['shots'] = {
                    'home': home_shots,
                    'away': away_shots
                }
            
            # Parse shots on target
            if '~SD÷' in response_text:
                home_sot = int(response_text.split('~SD÷')[1].split('¬')[0])
                away_sot = int(response_text.split('~SE÷')[1].split('¬')[0])
                stats['shots_on_target'] = {
                    'home': home_sot,
                    'away': away_sot
                }
            
            # Parse corners
            if '~SF÷' in response_text:
                home_corners = int(response_text.split('~SF÷')[1].split('¬')[0])
                away_corners = int(response_text.split('~SG÷')[1].split('¬')[0])
                stats['corners'] = {
                    'home': home_corners,
                    'away': away_corners
                }
            
            # Parse fouls
            if '~SH÷' in response_text:
                home_fouls = int(response_text.split('~SH÷')[1].split('¬')[0])
                away_fouls = int(response_text.split('~SI÷')[1].split('¬')[0])
                stats['fouls'] = {
                    'home': home_fouls,
                    'away': away_fouls
                }
            
            # Parse offsides
            if '~SJ÷' in response_text:
                home_offsides = int(response_text.split('~SJ÷')[1].split('¬')[0])
                away_offsides = int(response_text.split('~SK÷')[1].split('¬')[0])
                stats['offsides'] = {
                    'home': home_offsides,
                    'away': away_offsides
                }
            
            return DetailedStats(
                possession=stats['possession'],
                shots=stats['shots'],
                shots_on_target=stats['shots_on_target'],
                corners=stats['corners'],
                fouls=stats['fouls'],
                offsides=stats['offsides']
            )
            
        except Exception as e:
            logger.error(f"Error parsing statistics: {e}")
            return None


class StandingsParser:
    """Parser for tournament standings from FlashScore API"""
    
    def parse_standings(self, response_text: str) -> Optional[Standings]:
        """
        Parse standings data from API response
        
        Args:
            response_text: Raw standings API response
            
        Returns:
            Standings object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            teams = []
            groups = []
            
            # Check if response contains group data
            if '~TA÷' in response_text:
                # Parse group standings
                group_sections = response_text.split('~TA÷')
                
                for group_section in group_sections[1:]:
                    try:
                        group_name = group_section.split('¬')[0]
                        team_entries = group_section.split('~TB÷')
                        
                        group_teams = []
                        for team_entry in team_entries[1:]:
                            try:
                                team_data = {
                                    'position': team_entry.split('TC÷')[1].split('¬')[0],
                                    'name': team_entry.split('TD÷')[1].split('¬')[0],
                                    'games': team_entry.split('TE÷')[1].split('¬')[0],
                                    'wins': team_entry.split('TF÷')[1].split('¬')[0],
                                    'draws': team_entry.split('TG÷')[1].split('¬')[0],
                                    'losses': team_entry.split('TH÷')[1].split('¬')[0],
                                    'goals_for': team_entry.split('TI÷')[1].split('¬')[0],
                                    'goals_against': team_entry.split('TJ÷')[1].split('¬')[0],
                                    'goal_diff': team_entry.split('TK÷')[1].split('¬')[0],
                                    'points': team_entry.split('TL÷')[1].split('¬')[0]
                                }
                                group_teams.append(team_data)
                            except Exception as e:
                                logger.debug(f"Error parsing team entry: {e}")
                                continue
                                
                        groups.append({
                            'name': group_name,
                            'teams': group_teams
                        })
                        
                    except Exception as e:
                        logger.debug(f"Error parsing group section: {e}")
                        continue
            else:
                # Parse single table standings
                team_entries = response_text.split('~TB÷')
                
                for team_entry in team_entries[1:]:
                    try:
                        team_data = {
                            'position': team_entry.split('TC÷')[1].split('¬')[0],
                            'name': team_entry.split('TD÷')[1].split('¬')[0],
                            'games': team_entry.split('TE÷')[1].split('¬')[0],
                            'wins': team_entry.split('TF÷')[1].split('¬')[0],
                            'draws': team_entry.split('TG÷')[1].split('¬')[0],
                            'losses': team_entry.split('TH÷')[1].split('¬')[0],
                            'goals_for': team_entry.split('TI÷')[1].split('¬')[0],
                            'goals_against': team_entry.split('TJ÷')[1].split('¬')[0],
                            'goal_diff': team_entry.split('TK÷')[1].split('¬')[0],
                            'points': team_entry.split('TL÷')[1].split('¬')[0]
                        }
                        teams.append(team_data)
                    except Exception as e:
                        logger.debug(f"Error parsing team entry: {e}")
                        continue
            
            return Standings(
                teams=teams,
                groups=groups
            )
            
        except Exception as e:
            logger.error(f"Error parsing standings: {e}")
            return None


class IncidentsParser:
    """Parser for match incidents (goals, cards, subs) from FlashScore API"""
    
    def parse_incidents(self, response_text: str) -> Optional[MatchIncidents]:
        """
        Parse match incidents from API response
        
        Args:
            response_text: Raw incidents API response
            
        Returns:
            MatchIncidents object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            goals = []
            cards = []
            substitutions = []
            
            # Split into individual incidents
            incident_sections = response_text.split('~IA÷')
            
            for section in incident_sections[1:]:  # Skip first empty section
                try:
                    incident_type = section.split('IB÷')[1].split('¬')[0]
                    minute = section.split('IC÷')[1].split('¬')[0]
                    
                    if incident_type == 'GOAL':
                        # Parse goal
                        goal_data = {
                            'minute': minute,
                            'team': section.split('ID÷')[1].split('¬')[0],
                            'player': section.split('IE÷')[1].split('¬')[0],
                            'type': section.split('IF÷')[1].split('¬')[0] if 'IF÷' in section else '',
                            'score': section.split('IG÷')[1].split('¬')[0] if 'IG÷' in section else ''
                        }
                        goals.append(goal_data)
                        
                    elif incident_type == 'CARD':
                        # Parse card
                        card_data = {
                            'minute': minute,
                            'team': section.split('ID÷')[1].split('¬')[0],
                            'player': section.split('IE÷')[1].split('¬')[0],
                            'color': section.split('IF÷')[1].split('¬')[0],
                            'reason': section.split('IG÷')[1].split('¬')[0] if 'IG÷' in section else ''
                        }
                        cards.append(card_data)
                        
                    elif incident_type == 'SUBSTITUTION':
                        # Parse substitution
                        sub_data = {
                            'minute': minute,
                            'team': section.split('ID÷')[1].split('¬')[0],
                            'player_in': section.split('IE÷')[1].split('¬')[0],
                            'player_out': section.split('IF÷')[1].split('¬')[0]
                        }
                        substitutions.append(sub_data)
                        
                except Exception as e:
                    logger.debug(f"Error parsing incident: {e}")
                    continue
                    
            return MatchIncidents(
                goals=goals,
                cards=cards,
                substitutions=substitutions
            )
            
        except Exception as e:
            logger.error(f"Error parsing incidents: {e}")
            return None


class OddsParser:
    """Parser for betting odds from FlashScore API"""
    
    def parse_odds(self, response_text: str) -> Optional[BettingOdds]:
        """
        Parse betting odds from API response
        
        Args:
            response_text: Raw odds API response
            
        Returns:
            BettingOdds object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            odds = BettingOdds()
            
            # Parse main odds (1X2)
            if '~OA÷' in response_text:
                odds.home_win = float(response_text.split('~OA÷')[1].split('¬')[0])
                odds.draw = float(response_text.split('~OB÷')[1].split('¬')[0])
                odds.away_win = float(response_text.split('~OC÷')[1].split('¬')[0])
            
            # Parse over/under odds
            if '~OD÷' in response_text:
                odds.over_under_line = response_text.split('~OD÷')[1].split('¬')[0]
                odds.over_odds = float(response_text.split('~OE÷')[1].split('¬')[0])
                odds.under_odds = float(response_text.split('~OF÷')[1].split('¬')[0])
            
            # Parse both teams to score odds
            if '~OG÷' in response_text:
                odds.both_teams_score_yes = float(response_text.split('~OG÷')[1].split('¬')[0])
                odds.both_teams_score_no = float(response_text.split('~OH÷')[1].split('¬')[0])
            
            return odds
            
        except Exception as e:
            logger.error(f"Error parsing odds: {e}")
            return None


class HeadToHeadParser:
    """Parser for head-to-head statistics from FlashScore API"""

    def __init__(self):
        self.status_mapping = {
            '3': 'FINISHED',
            '5': 'CANCELLED',
            '4': 'POSTPONED',
            '11': 'AFTER_PENALTIES',
            '10': 'AFTER_EXTRA_TIME',
            '54': 'TECHNICAL_DEFEAT',
            '9': 'NO_SHOW'
        }
        self.result_mapping = {
            'l': 'LOST',
            'w': 'WIN',
            'd': 'DRAW',
            'lo': 'LOST',
            'wo': 'WIN'
        }

    def parse_h2h_data(self, response_text: str, home_team: str, away_team: str) -> dict:
        """
        Parse head-to-head data into standardized format
        
        Args:
            response_text: Raw API response
            home_team: Home team name
            away_team: Away team name
            
        Returns:
            Dictionary with formatted H2H data
        """
        try:
            if not response_text:
                return {"DATA": []}

            # Split response by sections
            sections = response_text.split('~KB÷')
            if len(sections) < 7:
                return {"DATA": []}

            # Create base structure
            result = {"DATA": []}
            
            # Overall tab
            overall_tab = {
                "TAB_NAME": "Overall",
                "GROUPS": [
                    self._create_group("Last matches: " + home_team, sections[1]),
                    self._create_group("Last matches: " + away_team, sections[2]),
                    self._create_group("Head-to-head matches", sections[3])
                ]
            }
            result["DATA"].append(overall_tab)
            
            # Home tab
            home_tab = {
                "TAB_NAME": f"{home_team} - Home",
                "GROUPS": [
                    self._create_group(f"Last matches: {home_team}", sections[4]),
                    self._create_group("Head-to-head matches", sections[5])
                ]
            }
            result["DATA"].append(home_tab)
            
            # Away tab
            away_tab = {
                "TAB_NAME": f"{away_team} - Away",
                "GROUPS": [
                    self._create_group(f"Last matches: {away_team}", sections[6]),
                    self._create_group("Head-to-head matches", sections[5])
                ]
            }
            result["DATA"].append(away_tab)
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing H2H data: {e}")
            return {"DATA": []}

    def _create_group(self, label: str, section_text: str) -> dict:
        """Create a formatted group with matches"""
        return {
            "GROUP_LABEL": label,
            "ITEMS": self._parse_section(section_text)
        }

    def _parse_section(self, section_text: str) -> List[dict]:
        """Parse a section into match items"""
        matches = []
        
        try:
            entries = section_text.split('~')
            for entry in entries:
                if 'KP÷' in entry:  # Match entry
                    try:
                        match = {
                            "START_TIME": int(entry.split('KC÷')[1].split('¬')[0]),
                            "EVENT_ID": entry.split('KP÷')[1].split('¬')[0],
                            "EVENT_NAME": entry.split('KF÷')[1].split('¬')[0],
                            "STAGE": self.status_mapping.get(entry.split('AC÷')[1].split('¬')[0], ""),
                            "COUNTRY_ID": int(entry.split('KG÷')[1].split('¬')[0]),
                            "COUNTRY_NAME": entry.split('KH÷')[1].split('¬')[0],
                            "EVENT_ACRONYM": entry.split('KI÷')[1].split('¬')[0],
                            "HOME_PARTICIPANT": entry.split('KJ÷')[1].split('¬')[0],
                            "HOME_PARTICIPANT_NAME_ONE": entry.split('FH÷')[1].split('¬')[0],
                            "HOME_PARTICIPANT_NAME_TWO": None,
                            "AWAY_PARTICIPANT": entry.split('KK÷')[1].split('¬')[0],
                            "AWAY_PARTICIPANT_NAME_ONE": entry.split('FK÷')[1].split('¬')[0],
                            "AWAY_PARTICIPANT_NAME_TWO": None,
                            "CURRENT_RESULT": entry.split('KL÷')[1].split('¬')[0],
                            "HOME_SCORE_FULL": entry.split('KU÷')[1].split('¬')[0],
                            "AWAY_SCORE_FULL": entry.split('KT÷')[1].split('¬')[0],
                            "HOME_IMAGES": [f"https://www.flashscore.com/res/image/data/{entry.split('EC÷')[1].split('¬')[0]}"],
                            "AWAY_IMAGES": [f"https://www.flashscore.com/res/image/data/{entry.split('ED÷')[1].split('¬')[0]}"],
                            "H_RESULT": self.result_mapping.get(entry.split('KN÷')[1].split('¬')[0], ""),
                            "TEAM_MARK": entry.split('KS÷')[1].split('¬')[0] if 'KS÷' in entry else ""
                        }
                        matches.append(match)
                    except Exception as e:
                        logger.debug(f"Error parsing match entry: {e}")
                        continue
                        
        except Exception as e:
            logger.warning(f"Error parsing section: {e}")
            
        return matches


class FlashScoreParser:
    """Main parser class that coordinates API calls and data processing"""

    def __init__(self, whitelist_manager: LeagueWhitelistManager = None):
        self.api = FlashScoreAPI()
        self.match_parser = MatchParser()
        self.h2h_parser = HeadToHeadParser()
        self.lineup_parser = LineupParser()
        self.commentary_parser = CommentaryParser()
        self.stats_parser = StatsParser()
        self.standings_parser = StandingsParser()
        self.incidents_parser = IncidentsParser()
        self.odds_parser = OddsParser()
        self.whitelist_manager = whitelist_manager or default_whitelist_manager

    def get_today_matches(self, sport_id: int, apply_whitelist: bool = True) -> List[Match]:
        """Get all matches for today for a specific sport"""
        logger.info(f"Fetching today's matches for sport {sport_id}")

        response_text = self.api.get_matches_for_day(sport_id, day_offset=0)
        if not response_text:
            logger.error("Failed to fetch today's matches")
            return []

        matches = self.match_parser.parse_matches_from_response(response_text, sport_id)
        logger.info(f"Found {len(matches)} matches before filtering")

        if apply_whitelist:
            matches = self._apply_whitelist_filter(matches, sport_id)
            logger.info(f"Found {len(matches)} matches after whitelist filtering")
        else:
            logger.info(f"Found {len(matches)} matches (whitelist disabled)")

        return matches

    def get_matches_for_date_range(self, sport_id: int, days: List[int], apply_whitelist: bool = True) -> List[Match]:
        """Get matches for multiple days"""
        all_matches = []

        for day_offset in days:
            logger.info(f"Fetching matches for day offset {day_offset}")
            response_text = self.api.get_matches_for_day(sport_id, day_offset)

            if response_text:
                matches = self.match_parser.parse_matches_from_response(response_text, sport_id)
                all_matches.extend(matches)
                logger.info(f"Found {len(matches)} matches for day {day_offset}")
            else:
                logger.warning(f"No data for day offset {day_offset}")

        if apply_whitelist and all_matches:
            original_count = len(all_matches)
            all_matches = self._apply_whitelist_filter(all_matches, sport_id)
            logger.info(f"Filtered {original_count} matches to {len(all_matches)} using whitelist")

        return all_matches

    def get_leagues_for_today(self, sport_id: int) -> Dict[str, List[str]]:
        """Get available leagues for today for a specific sport"""
        logger.info(f"Fetching available leagues for sport {sport_id}")

        response_text = self.api.get_matches_for_day(sport_id, day_offset=0)
        if not response_text:
            logger.error("Failed to fetch today's data")
            return {}

        leagues = {}
        try:
            # Split response by league sections
            league_sections = response_text.split('~ZA÷')

            for section in league_sections[1:]:  # Skip first empty section
                try:
                    # Extract league info
                    country_id = int(section.split('ZB÷')[1].split('¬')[0])
                    league_name = section.split('¬')[0]

                    # Count matches in this league
                    match_sections = section.split('~AA÷')
                    match_count = len(match_sections) - 1  # Subtract 1 for the first empty section

                    if league_name not in leagues:
                        leagues[league_name] = []

                    leagues[league_name].append(f"Country ID: {country_id}, Matches: {match_count}")

                except Exception as e:
                    logger.warning(f"Error parsing league section: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error parsing leagues response: {e}")

        logger.info(f"Found {len(leagues)} leagues")
        return leagues

    def _apply_whitelist_filter(self, matches: List[Match], sport_id: int) -> List[Match]:
        """
        Apply whitelist filtering to matches based on league/country criteria

        Args:
            matches: List of matches to filter
            sport_id: Sport ID for sport-specific filtering

        Returns:
            Filtered list of matches
        """
        if not self.whitelist_manager.enabled:
            logger.info("Whitelist filtering is disabled")
            return matches

        filtered_matches = []
        filtered_count = 0

        for match in matches:
            # Check if league is whitelisted using tournament_hash for TOURNAMENT_ID mode
            if (self.whitelist_manager.mode == WhitelistMode.TOURNAMENT_ID and
                hasattr(match, 'tournament_hash') and match.tournament_hash):
                # Use tournament hash for filtering (permanent ID)
                from whitelist import sp_good_ligs
                if match.tournament_hash in sp_good_ligs:
                    filtered_matches.append(match)
                else:
                    filtered_count += 1
                    logger.debug(f"Filtered out: {match.league_name} (Hash: {match.tournament_hash})")
            elif self.whitelist_manager.is_league_whitelisted(
                country_id=match.country_id,
                league_name=match.league_name,
                sport_id=sport_id
            ):
                filtered_matches.append(match)
            else:
                filtered_count += 1
                logger.debug(f"Filtered out: {match.league_name} (Country ID: {match.country_id})")

        logger.info(f"Whitelist filtering: kept {len(filtered_matches)} matches, filtered out {filtered_count}")
        return filtered_matches

    def get_whitelist_stats(self, sport_id: int = 1) -> Dict:
        """Get statistics about current whitelist configuration"""
        return self.whitelist_manager.get_whitelist_stats(sport_id)

    def update_whitelist_mode(self, mode: WhitelistMode) -> None:
        """Update whitelist filtering mode"""
        self.whitelist_manager.mode = mode
        logger.info(f"Updated whitelist mode to: {mode.value}")

    def enable_whitelist(self, enabled: bool = True) -> None:
        """Enable or disable whitelist filtering"""
        self.whitelist_manager.enabled = enabled
        logger.info(f"Whitelist filtering {'enabled' if enabled else 'disabled'}")

    def get_filtered_leagues_summary(self, sport_id: int, day_offset: int = 0) -> Dict:
        """
        Get summary of leagues before and after filtering

        Args:
            sport_id: Sport ID to analyze
            day_offset: Day offset (0=today, 1=tomorrow, etc.)

        Returns:
            Dictionary with filtering statistics
        """
        # Get all leagues without filtering
        response_text = self.api.get_matches_for_day(sport_id, day_offset)
        if not response_text:
            return {"error": "Failed to fetch data"}

        all_matches = self.match_parser.parse_matches_from_response(response_text, sport_id)

        # Analyze all leagues
        all_leagues = {}
        for match in all_matches:
            league_key = f"{match.league_name} (ID: {match.country_id})"
            if league_key not in all_leagues:
                all_leagues[league_key] = {
                    "league_name": match.league_name,
                    "country_id": match.country_id,
                    "match_count": 0,
                    "whitelisted": False
                }
            all_leagues[league_key]["match_count"] += 1

        # Check whitelist status for each league
        whitelisted_leagues = {}
        filtered_leagues = {}

        for league_key, league_info in all_leagues.items():
            is_whitelisted = self.whitelist_manager.is_league_whitelisted(
                country_id=league_info["country_id"],
                league_name=league_info["league_name"],
                sport_id=sport_id
            )

            league_info["whitelisted"] = is_whitelisted

            if is_whitelisted:
                whitelisted_leagues[league_key] = league_info
            else:
                filtered_leagues[league_key] = league_info

        return {
            "sport_id": sport_id,
            "day_offset": day_offset,
            "total_leagues": len(all_leagues),
            "whitelisted_leagues": len(whitelisted_leagues),
            "filtered_leagues": len(filtered_leagues),
            "total_matches": len(all_matches),
            "whitelisted_matches": sum(l["match_count"] for l in whitelisted_leagues.values()),
            "filtered_matches": sum(l["match_count"] for l in filtered_leagues.values()),
            "whitelist_enabled": self.whitelist_manager.enabled,
            "whitelist_mode": self.whitelist_manager.mode.value,
            "leagues_detail": {
                "whitelisted": whitelisted_leagues,
                "filtered": filtered_leagues
            }
        }
    
    def save_matches_to_json(self, matches: List[Match], filename: str,
                           include_whitelist_info: bool = True) -> bool:
        """Save matches to JSON file with optional whitelist information"""
        try:
            # Convert matches to dictionaries for JSON serialization
            matches_data = []
            for match in matches:
                match_dict = asdict(match)
                # Convert datetime to string for JSON serialization
                match_dict['start_time'] = match.start_time.isoformat()
                matches_data.append(match_dict)

            # Create output structure with whitelist information
            output_data = {
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(matches),
                "matches": matches_data
            }

            # Add whitelist information if requested
            if include_whitelist_info:
                output_data["whitelist_info"] = {
                    "enabled": self.whitelist_manager.enabled,
                    "mode": self.whitelist_manager.mode.value,
                    "filtering_applied": self.whitelist_manager.enabled,
                    "stats": self.whitelist_manager.get_whitelist_stats(
                        matches[0].sport_id if matches else 1
                    )
                }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(matches)} matches to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error saving matches to JSON: {e}")
            return False

    def save_matches_to_legacy_format(self, matches: List[Match], filename: str) -> bool:
        """
        Save matches in the original parser's JSON format for backward compatibility

        This method converts the modern Match objects back to the original format
        used by the legacy parser for maximum compatibility.
        """
        try:
            legacy_matches = []

            for match in matches:
                # Convert to original format:
                # [data_matcha, [liga,int(country_id)], team1, team2, '','','',sc1,sc2,'',status, url_game,'',
                #  short_team1, short_team2, '', '', '','','','', sport_id, id_game]

                legacy_match = [
                    match.start_time.strftime('%d.%m.%Y %H:%M'),  # data_matcha
                    [match.league_name, match.country_id],        # [liga, country_id]
                    match.home_team.name,                         # team1
                    match.away_team.name,                         # team2
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    match.home_score,                             # sc1
                    match.away_score,                             # sc2
                    '',                                           # placeholder
                    match.status,                                 # status
                    match.match_url,                              # url_game
                    '',                                           # placeholder
                    match.home_team.short_name,                   # short_team1
                    match.away_team.short_name,                   # short_team2
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    match.sport_id,                               # sport_id
                    match.event_id                                # id_game
                ]

                legacy_matches.append(legacy_match)

            # Create legacy output structure
            legacy_output = {
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(legacy_matches),
                "format": "legacy_compatible",
                "matches": legacy_matches,
                "whitelist_applied": self.whitelist_manager.enabled
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(legacy_output, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(legacy_matches)} matches to {filename} in legacy format")
            return True

        except Exception as e:
            logger.error(f"Error saving matches to legacy JSON format: {e}")
            return False

    def get_upcoming_matches(self, sport_id: int = 1, days_ahead: int = 7, apply_whitelist: bool = True, only_unstarted: bool = False) -> List[Match]:
        """
        Get upcoming matches for specified number of days ahead

        Args:
            sport_id: Sport ID (1=Football)
            days_ahead: Number of days to look ahead (default: 7)
            apply_whitelist: Whether to apply whitelist filtering
            only_unstarted: Whether to include only matches that haven't started

        Returns:
            List of upcoming matches
        """
        logger.info(f"Fetching upcoming matches for sport {sport_id}, {days_ahead} days ahead")

        # Get matches for multiple days (1 to days_ahead)
        days_range = list(range(1, days_ahead + 1))
        upcoming_matches = self.get_matches_for_date_range(sport_id, days_range, apply_whitelist)

        # Filter matches based on requirements
        filtered_matches = []
        for match in upcoming_matches:
            # Basic filter: exclude finished/cancelled matches
            if match.status in ['FINISHED', 'CANCELLED']:
                continue

            # Additional filter for unstarted matches only
            if only_unstarted:
                # Status codes for started/finished matches
                started_statuses = ['3', '5', '4', '11', '10', '54', '9', '12', '13', '38', '42']
                if match.status in started_statuses:
                    continue

            filtered_matches.append(match)

        logger.info(f"Found {len(filtered_matches)} {'unstarted' if only_unstarted else 'upcoming'} matches")
        return filtered_matches

    def get_match_statistics(self, match: Match) -> Optional[MatchStatistics]:
        """
        Get complete statistics for a match including all tabs data

        Args:
            match: Match object

        Returns:
            MatchStatistics object with all available data or None if failed
        """
        logger.info(f"Fetching statistics for match {match.event_id}: {match.home_team.name} vs {match.away_team.name}")

        try:
            # Initialize all data as None
            h2h_data = None
            betting_odds = None
            lineups = None
            commentary = None
            stats = None
            standings = None
            incidents = None

            # Get H2H data
            h2h_response = self.api.get_head_to_head_data(match.event_id)
            if h2h_response:
                h2h_data = self.h2h_parser.parse_h2h_data(h2h_response, match.home_team.name, match.away_team.name)
                logger.info(f"{'Successfully parsed' if h2h_data else 'Failed to parse'} H2H data")

            # Get betting odds
            odds_response = self.api.get_betting_odds(match.event_id)
            if odds_response:
                betting_odds = self.odds_parser.parse_odds(odds_response)
                logger.info(f"{'Successfully parsed' if betting_odds else 'Failed to parse'} betting odds")

            # Get lineups
            lineups_response = self.api.get_lineups(match.event_id)
            if lineups_response:
                lineups = self.lineup_parser.parse_lineups(lineups_response)
                logger.info(f"{'Successfully parsed' if lineups else 'Failed to parse'} lineups")

            # Get commentary
            commentary_response = self.api.get_match_commentary(match.event_id)
            if commentary_response:
                commentary = self.commentary_parser.parse_commentary(commentary_response)
                logger.info(f"{'Successfully parsed' if commentary else 'Failed to parse'} commentary")

            # Get stats
            stats_response = self.api.get_match_statistics(match.event_id)
            if stats_response:
                stats = self.stats_parser.parse_stats(stats_response)
                logger.info(f"{'Successfully parsed' if stats else 'Failed to parse'} stats")

            # Get standings (requires tournament ID)
            if hasattr(match, 'tournament_hash') and match.tournament_hash:
                standings_response = self.api.get_standings(match.tournament_hash)
                if standings_response:
                    standings = self.standings_parser.parse_standings(standings_response)
                    logger.info(f"{'Successfully parsed' if standings else 'Failed to parse'} standings")

            # Get incidents
            incidents_response = self.api.get_match_incidents(match.event_id)
            if incidents_response:
                incidents = self.incidents_parser.parse_incidents(incidents_response)
                logger.info(f"{'Successfully parsed' if incidents else 'Failed to parse'} incidents")

            return MatchStatistics(
                match=match,
                head_to_head=h2h_data,
                betting_odds=betting_odds,
                lineups=lineups,
                commentary=commentary,
                statistics=stats,
                standings=standings,
                incidents=incidents
            )

        except Exception as e:
            logger.error(f"Error getting statistics for match {match.event_id}: {e}")
            return None

    def save_match_with_statistics(self, match_stats: MatchStatistics, output_dir: str = "matches") -> bool:
        """
        Save individual match with its statistics to JSON file

        Args:
            match_stats: MatchStatistics object
            output_dir: Directory to save files

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)

            # Create filename based on match ID
            filename = f"{match_stats.match.event_id}.json"
            filepath = os.path.join(output_dir, filename)

            # Prepare data for JSON serialization
            match_dict = {
                "event_id": match_stats.match.event_id,
                "start_time": match_stats.match.start_time.isoformat(),
                "sport_id": match_stats.match.sport_id,
                "league_name": match_stats.match.league_name,
                "country_id": match_stats.match.country_id,
                "country_name": match_stats.match.country_name,
                "home_team": {
                    "name": match_stats.match.home_team.name,
                    "short_name": match_stats.match.home_team.short_name,
                    "image_url": match_stats.match.home_team.image_url
                },
                "away_team": {
                    "name": match_stats.match.away_team.name,
                    "short_name": match_stats.match.away_team.short_name,
                    "image_url": match_stats.match.away_team.image_url
                },
                "status": match_stats.match.status,
                "home_score": match_stats.match.home_score,
                "away_score": match_stats.match.away_score,
                "current_result": match_stats.match.current_result,
                "match_url": match_stats.match.match_url,
                "league_index": match_stats.match.league_index,
                "tournament_hash": match_stats.match.tournament_hash
            }

            output_data = {
                "timestamp": datetime.now().isoformat(),
                "match_id": match_stats.match.event_id,
                "match": match_dict,
                "head_to_head": match_stats.head_to_head if match_stats.head_to_head else None,
                "betting_odds": {
                    "home_win": match_stats.betting_odds.home_win if match_stats.betting_odds else None,
                    "draw": match_stats.betting_odds.draw if match_stats.betting_odds else None,
                    "away_win": match_stats.betting_odds.away_win if match_stats.betting_odds else None,
                    "over_under_line": match_stats.betting_odds.over_under_line if match_stats.betting_odds else None,
                    "over_odds": match_stats.betting_odds.over_odds if match_stats.betting_odds else None,
                    "under_odds": match_stats.betting_odds.under_odds if match_stats.betting_odds else None,
                    "both_teams_score_yes": match_stats.betting_odds.both_teams_score_yes if match_stats.betting_odds else None,
                    "both_teams_score_no": match_stats.betting_odds.both_teams_score_no if match_stats.betting_odds else None
                } if match_stats.betting_odds else None
            }

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved match statistics to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving match statistics: {e}")
            return False

    def save_match_in_standard_format(self, match_stats: MatchStatistics, filepath: str) -> bool:
        """
        Save match in standard JSON format

        Args:
            match_stats: MatchStatistics object
            filepath: Full path to save the file

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            match = match_stats.match

            # Create match_info structure
            match_info = {
                "event_id": match.event_id,
                "home_team": match.home_team.name,
                "away_team": match.away_team.name,
                "short_home": match.home_team.short_name,
                "short_away": match.away_team.short_name,
                "start_time": match.start_time.isoformat(),
                "league": match.league_name,
                "tournament_hash": match.tournament_hash,
                "current_result": match.current_result,
                "status": match.status,
                "match_url": match.match_url
            }

            # Get H2H data using existing parser.py function
            h2h_data = self._get_h2h_data_from_api(match.event_id, match.home_team.short_name, match.away_team.short_name)

            # Create the main structure with event_id as key
            output_data = {
                match.event_id: {
                    "match_info": match_info,
                    "h2h_data": h2h_data
                }
            }

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved match in Ireland format to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving match in Ireland format: {e}")
            return False

    def _get_h2h_data_from_api(self, event_id: str, short_home: str, short_away: str) -> dict:
        """
        Get H2H data from API using existing parser.py functions

        Args:
            event_id: Match event ID
            short_home: Home team short name
            short_away: Away team short name

        Returns:
            H2H data in Ireland format
        """
        try:
            import requests

            # Use the same URL pattern as in parser.py
            url = f'https://global.flashscore.ninja/2/x/feed/df_hh_1_{event_id}'

            response = requests.get(url, timeout=10)
            if response.status_code == 200 and response.text:
                all_dannie_text = response.text
                t_names = all_dannie_text.split('~KA÷')
                dannie_text_all = all_dannie_text.split('~KB÷')

                if len(dannie_text_all) >= 7:
                    # Import functions from parser.py
                    import sys
                    sys.path.append('/Users/<USER>/_1_2_flashscore_parser')
                    import parser as h2h_parser
                    poisk_h2h_json = h2h_parser.poisk_h2h_json
                    poisk_h2h_json_lichki = h2h_parser.poisk_h2h_json_lichki

                    dannie_overall_home = poisk_h2h_json(dannie_text_all[1])
                    dannie_overall_away = poisk_h2h_json(dannie_text_all[2])
                    dannie_overall_lichki = poisk_h2h_json_lichki(dannie_text_all[3])

                    overall_json = {
                        "TAB_NAME": t_names[1].split('¬')[0] if len(t_names) > 1 else "Overall",
                        "GROUPS": [dannie_overall_home, dannie_overall_away, dannie_overall_lichki]
                    }

                    h2h_json = {"DATA": [overall_json]}

                    # Add home and away specific data
                    if len(dannie_text_all) >= 7:
                        dannie_home_home = poisk_h2h_json(dannie_text_all[4])
                        dannie_home_lichki = poisk_h2h_json_lichki(dannie_text_all[5])
                        home_home_json = {
                            "TAB_NAME": f"{short_home} - Home",
                            "GROUPS": [dannie_home_home, dannie_home_lichki]
                        }
                        h2h_json["DATA"].append(home_home_json)

                        dannie_away_away = poisk_h2h_json(dannie_text_all[6])
                        away_away_json = {
                            "TAB_NAME": f"{short_away} - Away",
                            "GROUPS": [dannie_away_away, dannie_home_lichki]
                        }
                        h2h_json["DATA"].append(away_away_json)

                    return h2h_json

        except Exception as e:
            logger.warning(f"Error getting H2H data from API: {e}")

        return {"DATA": []}

    def _poisk_h2h_json(self, dannie_text: str) -> dict:
        """
        Parse H2H JSON data (copied from parser.py poisk_h2h_json function)
        """
        dannie_matchs = {"GROUP_LABEL": "", "ITEMS": []}
        stage = {'3': 'FINISHED', '5': 'CANCELLED', '4': 'Postponed', '11': 'After Penalties',
                '10': 'After extra time', '54': 'Those. defeat', '9': 'No-show'}
        result = {"l": "LOST", "w": "WIN", "d": "DRAW", "lo": "LOST", "wo": "WIN"}

        try:
            h2hs_homes = dannie_text.split('¬~')
            dannie_matchs["GROUP_LABEL"] = h2hs_homes[0]

            for h2h in h2hs_homes[1:]:
                match_item = {
                    "START_TIME": "", "EVENT_ID": "", "EVENT_NAME": "", "STAGE": "", "COUNTRY_ID": "", "COUNTRY_NAME": "",
                    "EVENT_ACRONYM": "", "HOME_PARTICIPANT": "", "HOME_PARTICIPANT_NAME_ONE": "", "HOME_PARTICIPANT_NAME_TWO": None,
                    "AWAY_PARTICIPANT": "", "AWAY_PARTICIPANT_NAME_ONE": "", "AWAY_PARTICIPANT_NAME_TWO": None, "CURRENT_RESULT": "",
                    "HOME_SCORE_FULL": "", "AWAY_SCORE_FULL": "", "HOME_IMAGES": [""], "AWAY_IMAGES": [""], "H_RESULT": "", "TEAM_MARK": ""
                }
                try:
                    match_item["EVENT_ID"] = h2h.split('KP÷')[1].split('¬')[0]
                    match_item["START_TIME"] = int(h2h.split('KC÷')[1].split('¬')[0])
                    match_item["EVENT_NAME"] = h2h.split('KF÷')[1].split('¬')[0]
                    try:
                        match_item["STAGE"] = stage[h2h.split('AC÷')[1].split('¬')[0]]
                    except:
                        pass

                    match_item["COUNTRY_ID"] = int(h2h.split('KG÷')[1].split('¬')[0])
                    match_item["COUNTRY_NAME"] = h2h.split('KH÷')[1].split('¬')[0]
                    match_item["EVENT_ACRONYM"] = h2h.split('KI÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT"] = h2h.split('KJ÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT_NAME_ONE"] = h2h.split('FH÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT"] = h2h.split('KK÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT_NAME_ONE"] = h2h.split('FK÷')[1].split('¬')[0]
                    match_item["CURRENT_RESULT"] = h2h.split('KL÷')[1].split('¬')[0]
                    match_item["HOME_SCORE_FULL"] = h2h.split('KU÷')[1].split('¬')[0]
                    match_item["AWAY_SCORE_FULL"] = h2h.split('KT÷')[1].split('¬')[0]
                    match_item["HOME_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('EC÷')[1].split('¬')[0]}"
                    match_item["AWAY_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('ED÷')[1].split('¬')[0]}"
                    match_item["H_RESULT"] = result[h2h.split('KN÷')[1].split('¬')[0]]
                    match_item["TEAM_MARK"] = h2h.split('KS÷')[1].split('¬')[0]

                    dannie_matchs["ITEMS"].append(match_item)
                except:
                    pass
        except:
            pass

        return dannie_matchs

    def _poisk_h2h_json_lichki(self, dannie_text: str) -> dict:
        """
        Parse H2H JSON data for head-to-head matches (copied from parser.py poisk_h2h_json_lichki function)
        """
        dannie_matchs = {"GROUP_LABEL": "", "ITEMS": []}
        stage = {'3': 'FINISHED', '5': 'CANCELLED', '4': 'Postponed', '11': 'After Penalties',
                '10': 'After extra time', '54': 'Those. defeat', '9': 'No-show'}
        result = {"l": "LOST", "w": "WIN", "d": "DRAW", "lo": "LOST", "wo": "WIN"}

        try:
            h2hs_homes = dannie_text.split('¬~')
            dannie_matchs["GROUP_LABEL"] = h2hs_homes[0]

            for h2h in h2hs_homes[1:]:
                match_item = {
                    "START_TIME": "", "EVENT_ID": "", "EVENT_NAME": "", "STAGE": "", "COUNTRY_ID": "", "COUNTRY_NAME": "",
                    "EVENT_ACRONYM": "", "HOME_PARTICIPANT": "", "HOME_PARTICIPANT_NAME_ONE": "", "HOME_PARTICIPANT_NAME_TWO": None,
                    "AWAY_PARTICIPANT": "", "AWAY_PARTICIPANT_NAME_ONE": "", "AWAY_PARTICIPANT_NAME_TWO": None, "CURRENT_RESULT": "",
                    "HOME_SCORE_FULL": "", "AWAY_SCORE_FULL": "", "HOME_IMAGES": [""], "AWAY_IMAGES": [""]
                }
                try:
                    match_item["EVENT_ID"] = h2h.split('KP÷')[1].split('¬')[0]
                    match_item["START_TIME"] = int(h2h.split('KC÷')[1].split('¬')[0])
                    match_item["EVENT_NAME"] = h2h.split('KF÷')[1].split('¬')[0]
                    try:
                        match_item["STAGE"] = stage[h2h.split('AC÷')[1].split('¬')[0]]
                    except:
                        pass

                    match_item["COUNTRY_ID"] = int(h2h.split('KG÷')[1].split('¬')[0])
                    match_item["COUNTRY_NAME"] = h2h.split('KH÷')[1].split('¬')[0]
                    match_item["EVENT_ACRONYM"] = h2h.split('KI÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT"] = h2h.split('KJ÷')[1].split('¬')[0]
                    match_item["HOME_PARTICIPANT_NAME_ONE"] = h2h.split('FH÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT"] = h2h.split('KK÷')[1].split('¬')[0]
                    match_item["AWAY_PARTICIPANT_NAME_ONE"] = h2h.split('FK÷')[1].split('¬')[0]
                    match_item["CURRENT_RESULT"] = h2h.split('KL÷')[1].split('¬')[0]
                    match_item["HOME_SCORE_FULL"] = h2h.split('KU÷')[1].split('¬')[0]
                    match_item["AWAY_SCORE_FULL"] = h2h.split('KT÷')[1].split('¬')[0]
                    match_item["HOME_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('EC÷')[1].split('¬')[0]}"
                    match_item["AWAY_IMAGES"][0] = f"https://www.flashscore.com/res/image/data/{h2h.split('ED÷')[1].split('¬')[0]}"

                    dannie_matchs["ITEMS"].append(match_item)
                except:
                    pass
        except:
            pass

        return dannie_matchs

    def save_matches_in_standard_format(self, matches: List[Match], league_name: str = None) -> bool:
        """
        Save all matches in standard JSON format

        Args:
            matches: List of Match objects
            league_name: Optional league name for filename

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            if not matches:
                logger.warning("No matches to save")
                return False

            # Create filename based on league name
            if league_name:
                safe_league_name = league_name.lower().replace(" ", "_").replace(":", "")
                filename = f"{safe_league_name}_stats.json"
            else:
                # Use first match's league name
                safe_league_name = matches[0].league_name.lower().replace(" ", "_").replace(":", "")
                filename = f"{safe_league_name}_stats.json"

            # Create full filepath
            filepath = f"/Users/<USER>/_1_2_flashscore_parser/{filename}"

            # Collect all matches data
            all_matches_data = {}

            for match in matches:
                try:
                    # Create match_info
                    match_info = {
                        "event_id": match.event_id,
                        "home_team": match.home_team.name,
                        "away_team": match.away_team.name,
                        "short_home": match.home_team.short_name,
                        "short_away": match.away_team.short_name,
                        "start_time": match.start_time.isoformat(),
                        "league": match.league_name,
                        "tournament_hash": match.tournament_hash,
                        "current_result": match.current_result,
                        "status": match.status,
                        "match_url": match.match_url
                    }

                    # Get H2H data
                    h2h_data = self._get_h2h_data_from_api(match.event_id, match.home_team.short_name, match.away_team.short_name)

                    # Add to collection
                    all_matches_data[match.event_id] = {
                        "match_info": match_info,
                        "h2h_data": h2h_data
                    }

                    # Small delay to avoid overwhelming API
                    time.sleep(0.1)

                except Exception as e:
                    logger.warning(f"Error processing match {match.event_id}: {e}")
                    continue

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(all_matches_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(all_matches_data)} matches in Ireland format to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving matches in Ireland format: {e}")
            return False

    def parse_and_save_individual_matches_simple(self, sport_id: int = 1, days_ahead: int = 3, folder_name: str = "matches") -> Dict:
        """
        Parse upcoming matches and save each match as individual JSON file

        Args:
            sport_id: Sport ID (1=Football)
            days_ahead: Number of days to look ahead
            folder_name: Folder name to save matches

        Returns:
            Dictionary with processing results
        """
        logger.info(f"Starting individual matches parsing, sport {sport_id}, {days_ahead} days ahead")

        # Get upcoming matches with whitelist filtering, only unstarted matches
        upcoming_matches = self.get_upcoming_matches(sport_id, days_ahead, apply_whitelist=True, only_unstarted=True)

        if not upcoming_matches:
            logger.warning("No upcoming unstarted matches found")
            return {
                "total_matches": 0,
                "processed_matches": 0,
                "successful_saves": 0,
                "errors": 0
            }

        # Save each match individually
        success_count = self.save_matches_individually(upcoming_matches, folder_name)

        results = {
            "total_matches": len(upcoming_matches),
            "processed_matches": len(upcoming_matches),
            "successful_saves": success_count,
            "errors": len(upcoming_matches) - success_count,
            "success_rate": f"{(success_count/len(upcoming_matches)*100):.1f}%" if upcoming_matches else "0%"
        }

        logger.info(f"Completed individual matches processing: {success_count}/{len(upcoming_matches)} saved")
        return results

    def save_matches_individually(self, matches: List[Match], folder_name: str = "matches") -> int:
        """
        Save each match as individual JSON file

        Args:
            matches: List of Match objects
            folder_name: Folder name to save files

        Returns:
            Number of successfully saved matches
        """
        try:
            import os
            import sys
            sys.path.append('/Users/<USER>/_1_2_flashscore_parser')
            import parser as h2h_parser

            # Create folder if not exists
            folder_path = f"/Users/<USER>/_1_2_flashscore_parser/{folder_name}"
            os.makedirs(folder_path, exist_ok=True)

            saved_count = 0

            for match in matches:
                try:
                    # Create match_info
                    match_info = {
                        "event_id": match.event_id,
                        "home_team": match.home_team.name,
                        "away_team": match.away_team.name,
                        "short_home": match.home_team.short_name,
                        "short_away": match.away_team.short_name,
                        "start_time": match.start_time.isoformat(),
                        "league": match.league_name,
                        "tournament_hash": match.tournament_hash,
                        "current_result": match.current_result,
                        "status": match.status,
                        "match_url": match.match_url
                    }

                    # Get H2H data using existing parser.py functions
                    h2h_data = self._get_h2h_data_for_match(match.event_id, match.home_team.short_name, match.away_team.short_name)

                    # Create match data structure
                    match_data = {
                        match.event_id: {
                            "match_info": match_info,
                            "h2h_data": h2h_data
                        }
                    }

                    # Save to individual file
                    filename = f"{match.event_id}.json"
                    filepath = os.path.join(folder_path, filename)

                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(match_data, f, ensure_ascii=False, indent=2)

                    saved_count += 1
                    logger.info(f"Saved match {saved_count}: {filename}")
                    time.sleep(0.1)  # Small delay to avoid overwhelming API

                except Exception as e:
                    logger.warning(f"Error saving match {match.event_id}: {e}")
                    continue

            logger.info(f"Successfully saved {saved_count} matches to {folder_path}")
            return saved_count

        except Exception as e:
            logger.error(f"Error in save_matches_individually: {e}")
            return 0

    def _get_h2h_data_for_match(self, event_id: str, short_home: str, short_away: str) -> dict:
        """
        Get H2H data for match using existing parser.py functions

        Args:
            event_id: Match event ID
            short_home: Home team short name
            short_away: Away team short name

        Returns:
            H2H data in correct format
        """
        try:
            import sys
            sys.path.append('/Users/<USER>/_1_2_flashscore_parser')
            import parser as h2h_parser

            url = f'https://global.flashscore.ninja/2/x/feed/df_hh_1_{event_id}'

            response = self.api_client.make_request(url)
            if response and response.text:
                all_data_text = response.text
                tab_names = all_data_text.split('~KA÷')
                data_sections = all_data_text.split('~KB÷')

                if len(data_sections) >= 7:
                    # Overall data using parser.py functions
                    overall_home = h2h_parser.poisk_h2h_json(data_sections[1])
                    overall_away = h2h_parser.poisk_h2h_json(data_sections[2])
                    overall_h2h = h2h_parser.poisk_h2h_json_lichki(data_sections[3])

                    overall_tab = {
                        "TAB_NAME": tab_names[1].split('¬')[0] if len(tab_names) > 1 else "Overall",
                        "GROUPS": [overall_home, overall_away, overall_h2h]
                    }

                    h2h_data = {"DATA": [overall_tab]}

                    # Home matches
                    home_matches = h2h_parser.poisk_h2h_json(data_sections[4])
                    home_h2h = h2h_parser.poisk_h2h_json_lichki(data_sections[5])
                    home_tab = {
                        "TAB_NAME": f"{short_home} - Home",
                        "GROUPS": [home_matches, home_h2h]
                    }
                    h2h_data["DATA"].append(home_tab)

                    # Away matches
                    away_matches = h2h_parser.poisk_h2h_json(data_sections[6])
                    away_tab = {
                        "TAB_NAME": f"{short_away} - Away",
                        "GROUPS": [away_matches, home_h2h]
                    }
                    h2h_data["DATA"].append(away_tab)

                    return h2h_data

        except Exception as e:
            logger.warning(f"Error getting H2H data for {event_id}: {e}")

        return {"DATA": []}

    def parse_and_save_individual_matches(self, sport_id: int = 1, days_ahead: int = 3, folder_name: str = "matches") -> Dict:
        """Simple working method to parse and save matches"""
        import requests
        import os
        import sys
        sys.path.append('/Users/<USER>/_1_2_flashscore_parser')
        import parser as h2h_parser
        from whitelist import sp_good_ligs

        logger.info(f"Starting simple matches parsing, sport {sport_id}, {days_ahead} days ahead")

        all_matches = []

        # Parse matches for each day
        for day in range(0, days_ahead):
            url = f'https://global.flashscore.ninja/2/x/feed/f_{sport_id}_{day}_3_en_1'

            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/111.0',
                    'Accept': '*/*',
                    'Origin': 'https://www.flashscore.com',
                    'Referer': 'https://www.flashscore.com/',
                    'x-fsign': 'SW9D1eZo'
                }

                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code != 200:
                    continue

                page_text = response.text.split('~ZA÷')

                for text_section in page_text[1:]:
                    country_id = text_section.split('ZB÷')[1].split('¬')[0]
                    league = text_section.split('¬')[0]

                    # Extract tournament_hash for whitelist
                    tournament_hash = ""
                    lines = text_section.split('¬')
                    for line in lines:
                        if line.startswith('ZC÷'):
                            tournament_hash = line[3:]
                            break

                    # Check whitelist
                    if tournament_hash not in sp_good_ligs:
                        continue

                    games = text_section.split('~AA÷')
                    for game in games[1:]:
                        try:
                            status = game.split('AC÷')[1].split('¬')[0]

                            # Filter only unstarted matches
                            started_statuses = ['3', '5', '4', '11', '10', '54', '9', '12', '13', '38', '42']
                            if status in started_statuses:
                                continue

                            match_id = game.split('¬')[0]
                            match_time = datetime.fromtimestamp(int(game.split('AD÷')[1].split('¬')[0])).isoformat()
                            home_team = game.split('AE÷')[1].split('¬')[0]
                            away_team = game.split('AF÷')[1].split('¬')[0]
                            home_short = game.split('WM÷')[1].split('¬')[0]
                            away_short = game.split('WN÷')[1].split('¬')[0]

                            try:
                                home_score = game.split('AG÷')[1].split('¬')[0]
                                away_score = game.split('AH÷')[1].split('¬')[0]
                                current_result = f"{home_score}:{away_score}" if home_score and away_score else ""
                            except:
                                current_result = ""

                            match_url = f'https://www.flashscore.com/match/{match_id}/#/match-summary'

                            match_data = {
                                'event_id': match_id,
                                'home_team': home_team,
                                'away_team': away_team,
                                'short_home': home_short,
                                'short_away': away_short,
                                'start_time': match_time,
                                'league': league,
                                'tournament_hash': tournament_hash,
                                'current_result': current_result,
                                'status': status,
                                'match_url': match_url
                            }

                            all_matches.append(match_data)

                        except:
                            continue

            except Exception as e:
                logger.warning(f"Error parsing day {day}: {e}")
                continue

        if not all_matches:
            return {"total_matches": 0, "processed_matches": 0, "successful_saves": 0, "errors": 0}

        # Save matches individually
        folder_path = f"/Users/<USER>/_1_2_flashscore_parser/{folder_name}"
        os.makedirs(folder_path, exist_ok=True)

        saved_count = 0
        for match in all_matches:
            try:
                # Get H2H data
                h2h_url = f'https://global.flashscore.ninja/2/x/feed/df_hh_1_{match["event_id"]}'
                h2h_response = requests.get(h2h_url, headers=headers, timeout=10)

                h2h_data = {"DATA": []}
                if h2h_response.status_code == 200 and h2h_response.text:
                    all_data_text = h2h_response.text
                    tab_names = all_data_text.split('~KA÷')
                    data_sections = all_data_text.split('~KB÷')

                    if len(data_sections) >= 7:
                        # Overall tab
                        overall_home = h2h_parser.poisk_h2h_json(data_sections[1])
                        overall_away = h2h_parser.poisk_h2h_json(data_sections[2])
                        overall_h2h = h2h_parser.poisk_h2h_json_lichki(data_sections[3])

                        overall_tab = {
                            "TAB_NAME": tab_names[1].split('¬')[0] if len(tab_names) > 1 else "Overall",
                            "GROUPS": [overall_home, overall_away, overall_h2h]
                        }
                        h2h_data["DATA"].append(overall_tab)

                        # H2H tab (отдельный таб для head-to-head матчей)
                        h2h_tab = {
                            "TAB_NAME": f"{match['short_home']} vs {match['short_away']}",
                            "GROUPS": [overall_h2h]
                        }
                        h2h_data["DATA"].append(h2h_tab)

                        # Home tab
                        home_matches = h2h_parser.poisk_h2h_json(data_sections[4])
                        home_h2h = h2h_parser.poisk_h2h_json_lichki(data_sections[5])
                        home_tab = {
                            "TAB_NAME": f"{match['short_home']} - Home",
                            "GROUPS": [home_matches, home_h2h]
                        }
                        h2h_data["DATA"].append(home_tab)

                        # Away tab
                        away_matches = h2h_parser.poisk_h2h_json(data_sections[6])
                        away_tab = {
                            "TAB_NAME": f"{match['short_away']} - Away",
                            "GROUPS": [away_matches, home_h2h]
                        }
                        h2h_data["DATA"].append(away_tab)

                # Create match file
                match_file_data = {
                    match["event_id"]: {
                        "match_info": match,
                        "h2h_data": h2h_data
                    }
                }

                filename = f"{match['event_id']}.json"
                filepath = os.path.join(folder_path, filename)

                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(match_file_data, f, ensure_ascii=False, indent=2)

                saved_count += 1
                logger.info(f"Saved match {saved_count}: {filename}")
                time.sleep(0.1)

            except Exception as e:
                logger.warning(f"Error saving match {match['event_id']}: {e}")
                continue

        results = {
            "total_matches": len(all_matches),
            "processed_matches": len(all_matches),
            "successful_saves": saved_count,
            "errors": len(all_matches) - saved_count,
            "success_rate": f"{(saved_count/len(all_matches)*100):.1f}%" if all_matches else "0%"
        }

        logger.info(f"Completed: {saved_count}/{len(all_matches)} matches saved")
        return results

    def parse_and_save_upcoming_matches(self, sport_id: int = 1, days_ahead: int = 7, output_dir: str = "matches") -> Dict:
        """
        Main method: Parse upcoming matches by whitelist and save each with statistics

        Args:
            sport_id: Sport ID (1=Football)
            days_ahead: Number of days to look ahead
            output_dir: Directory to save match files

        Returns:
            Dictionary with processing results
        """
        logger.info(f"Starting upcoming matches parsing for sport {sport_id}")

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Get upcoming matches with whitelist filtering
        upcoming_matches = self.get_upcoming_matches(sport_id, days_ahead, apply_whitelist=True)

        if not upcoming_matches:
            logger.warning("No upcoming matches found")
            return {
                "total_matches": 0,
                "processed_matches": 0,
                "successful_saves": 0,
                "errors": 0,
                "output_directory": output_dir
            }

        logger.info(f"Processing {len(upcoming_matches)} upcoming matches...")

        processed = 0
        successful_saves = 0
        errors = 0

        for match in upcoming_matches:
            try:
                # Get match statistics
                match_stats = self.get_match_statistics(match)

                if match_stats:
                    # Save match with statistics
                    if self.save_match_with_statistics(match_stats, output_dir):
                        successful_saves += 1
                    else:
                        errors += 1
                else:
                    errors += 1

                processed += 1

                # Add small delay to avoid overwhelming the API
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error processing match {match.event_id}: {e}")
                errors += 1
                processed += 1

        results = {
            "total_matches": len(upcoming_matches),
            "processed_matches": processed,
            "successful_saves": successful_saves,
            "errors": errors,
            "output_directory": output_dir,
            "success_rate": f"{(successful_saves/len(upcoming_matches)*100):.1f}%" if upcoming_matches else "0%"
        }

        logger.info(f"Completed processing: {successful_saves}/{len(upcoming_matches)} matches saved successfully")
        return results


def main():
    """Main function to demonstrate the parser with upcoming matches and statistics"""
    parser = FlashScoreParser()

    print("="*60)
    print("FlashScore Parser - Upcoming Matches (4 Whitelisted Leagues Only)")
    print("="*60)

    # Configure parser to use TOURNAMENT_ID mode for exact 4 leagues
    parser.update_whitelist_mode(WhitelistMode.TOURNAMENT_ID)
    parser.enable_whitelist(True)

    # Show whitelist configuration
    from whitelist import sp_good_ligs, league_details
    print(f"Whitelist Configuration:")
    print(f"  Mode: TOURNAMENT_ID (exact hash matching)")
    print(f"  Enabled: True")
    print(f"  Target Leagues: {len(sp_good_ligs)}")

    for hash_id in sp_good_ligs:
        if hash_id in league_details:
            league = league_details[hash_id]
            print(f"    - {league['name']} (Hash: {hash_id})")

    # Parse and save upcoming matches with statistics
    print(f"\n🚀 Starting upcoming matches parsing (4 leagues only)...")
    results = parser.parse_and_save_upcoming_matches(
        sport_id=SportType.FOOTBALL.value,
        days_ahead=7,
        output_dir="football_matches"
    )

    # Show results
    print(f"\n📊 Processing Results:")
    print(f"  Total upcoming matches found: {results['total_matches']}")
    print(f"  Matches processed: {results['processed_matches']}")
    print(f"  Successfully saved: {results['successful_saves']}")
    print(f"  Errors: {results['errors']}")
    print(f"  Success rate: {results['success_rate']}")
    print(f"  Output directory: {results['output_directory']}")

    if results['successful_saves'] > 0:
        print(f"\n✅ Successfully saved {results['successful_saves']} matches with statistics!")
        print(f"📁 Check the '{results['output_directory']}' folder for individual match files")
        print(f"💡 Each file is named {'{match_id}.json'} and contains:")
        print(f"   - Match details (teams, time, league)")
        print(f"   - Head-to-head statistics")
        print(f"   - Historical match data")
        print(f"   - Only from the 4 whitelisted leagues")
    else:
        print(f"\n❌ No matches were saved successfully")
        print(f"💡 This might mean no upcoming matches in the 4 target leagues")

    # Show sample of upcoming matches
    upcoming_matches = parser.get_upcoming_matches(SportType.FOOTBALL.value, days_ahead=3)
    if upcoming_matches:
        print(f"\n📅 Upcoming matches from whitelisted leagues (next 3 days):")
        for i, match in enumerate(upcoming_matches):
            print(f"  {i+1}. {match.home_team.name} vs {match.away_team.name}")
            print(f"     League: {match.league_name}")
            print(f"     Time: {match.start_time.strftime('%Y-%m-%d %H:%M')}")
            print(f"     Match ID: {match.event_id}")
            print(f"     Tournament Hash: {match.tournament_hash}")
            print()
    else:
        print(f"\n❌ No upcoming matches found in the 4 whitelisted leagues")

    print(f"🎯 Parser configured for ONLY 4 whitelisted leagues")
    print(f"📈 Each match file contains complete statistics and head-to-head data")


if __name__ == "__main__":
    main()
