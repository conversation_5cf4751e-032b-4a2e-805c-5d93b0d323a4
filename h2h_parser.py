"""
Head-to-Head <PERSON>rser <PERSON><PERSON><PERSON>
Handles parsing of H2H data from FlashScore API responses with the exact format structure.
"""

from typing import List, Dict

from utils import (
    STATUS_MAPPING, H2H_RESULT_MAPPING, logger, 
    safe_extract, safe_extract_int, create_image_url, 
    get_team_abbreviation, validate_match_data
)


class HeadToHeadParser:
    """Parser for head-to-head statistics from FlashScore API"""

    def __init__(self):
        self.status_mapping = STATUS_MAPPING
        self.result_mapping = H2H_RESULT_MAPPING

    def parse_h2h_data(self, response_text: str, home_team: str, away_team: str) -> dict:
        """
        Parse head-to-head data into standardized format with 4 tabs
        
        Args:
            response_text: Raw API response
            home_team: Home team name
            away_team: Away team name
            
        Returns:
            Dictionary with formatted H2H data
        """
        try:
            if not response_text:
                return {"DATA": []}

            # Split response by sections
            sections = response_text.split('~KB÷')
            if len(sections) < 7:
                return {"DATA": []}

            # Get team abbreviations (first 3 characters)
            home_abbr = get_team_abbreviation(home_team)
            away_abbr = get_team_abbreviation(away_team)

            # Create base structure
            result = {"DATA": []}
            
            # Overall tab
            overall_tab = {
                "TAB_NAME": "Overall",
                "GROUPS": [
                    self._create_group("Last matches: " + home_team, sections[1]),
                    self._create_group("Last matches: " + away_team, sections[2]),
                    self._create_group("Head-to-head matches", sections[3])
                ]
            }
            result["DATA"].append(overall_tab)
            
            # Home tab
            home_tab = {
                "TAB_NAME": f"{home_abbr} - Home",
                "GROUPS": [
                    self._create_group(f"Last matches: {home_team}", sections[4]),
                    self._create_group("Head-to-head matches", sections[5])
                ]
            }
            result["DATA"].append(home_tab)
            
            # Away tab
            away_tab = {
                "TAB_NAME": f"{away_abbr} - Away",
                "GROUPS": [
                    self._create_group(f"Last matches: {away_team}", sections[6]),
                    self._create_group("Head-to-head matches", sections[5])
                ]
            }
            result["DATA"].append(away_tab)
            
            # Fourth tab - H2H specific matches (as per user's memory requirement for 4 tabs)
            h2h_tab = {
                "TAB_NAME": f"{home_abbr} vs {away_abbr}",
                "GROUPS": [
                    self._create_group("Head-to-head matches", sections[3])
                ]
            }
            result["DATA"].append(h2h_tab)
            
            return result
            
        except Exception as e:
            logger.error(f"Error parsing H2H data: {e}")
            return {"DATA": []}

    def _create_group(self, label: str, section_text: str) -> dict:
        """Create a formatted group with matches"""
        return {
            "GROUP_LABEL": label,
            "ITEMS": self._parse_section(section_text)
        }

    def _parse_section(self, section_text: str) -> List[dict]:
        """Parse a section into match items with proper error handling"""
        matches = []
        
        try:
            entries = section_text.split('~')
            for entry in entries:
                if 'KP÷' in entry:  # Match entry
                    try:
                        match = {
                            "START_TIME": safe_extract_int(entry, 'KC÷'),
                            "EVENT_ID": safe_extract(entry, 'KP÷'),
                            "EVENT_NAME": safe_extract(entry, 'KF÷'),
                            "STAGE": self.status_mapping.get(safe_extract(entry, 'AC÷'), "FINISHED"),
                            "COUNTRY_ID": safe_extract_int(entry, 'KG÷'),
                            "COUNTRY_NAME": safe_extract(entry, 'KH÷'),
                            "EVENT_ACRONYM": safe_extract(entry, 'KI÷'),
                            "HOME_PARTICIPANT": safe_extract(entry, 'KJ÷'),
                            "HOME_PARTICIPANT_NAME_ONE": safe_extract(entry, 'FH÷'),
                            "HOME_PARTICIPANT_NAME_TWO": None,
                            "AWAY_PARTICIPANT": safe_extract(entry, 'KK÷'),
                            "AWAY_PARTICIPANT_NAME_ONE": safe_extract(entry, 'FK÷'),
                            "AWAY_PARTICIPANT_NAME_TWO": None,
                            "CURRENT_RESULT": safe_extract(entry, 'KL÷'),
                            "HOME_SCORE_FULL": safe_extract(entry, 'KU÷'),
                            "AWAY_SCORE_FULL": safe_extract(entry, 'KT÷'),
                            "HOME_IMAGES": [create_image_url(safe_extract(entry, 'EC÷'))] if safe_extract(entry, 'EC÷') else [""],
                            "AWAY_IMAGES": [create_image_url(safe_extract(entry, 'ED÷'))] if safe_extract(entry, 'ED÷') else [""],
                            "H_RESULT": self.result_mapping.get(safe_extract(entry, 'KN÷'), ""),
                            "TEAM_MARK": safe_extract(entry, 'KS÷')
                        }
                        
                        # Only add match if it has essential data
                        if validate_match_data(match):
                            matches.append(match)
                            
                    except Exception as e:
                        logger.debug(f"Error parsing match entry: {e}")
                        continue
                        
        except Exception as e:
            logger.warning(f"Error parsing section: {e}")
            
        return matches
