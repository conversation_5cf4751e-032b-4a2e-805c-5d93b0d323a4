"""
Main FlashScore Parser Module
Contains the main FlashScoreParser class that coordinates all parsing operations.
"""

import json
import os
from datetime import datetime
from dataclasses import asdict
from typing import Dict, List, Optional

from flashscore_api import FlashScoreAPI
from league_whitelist_config import (
    LeagueWhitelistManager, WhitelistMode, default_whitelist_manager
)

from data_models import Match, MatchStatistics
from match_parser import MatchParser
from h2h_parser import HeadToHeadParser
from stats_parsers import (
    LineupParser, CommentaryParser, StatsParser, 
    StandingsParser, IncidentsParser, OddsParser
)
from utils import logger, ensure_directory_exists


class FlashScoreParser:
    """Main parser class that coordinates API calls and data processing"""

    def __init__(self, whitelist_manager: LeagueWhitelistManager = None):
        self.api = FlashScoreAPI()
        self.match_parser = MatchParser()
        self.h2h_parser = HeadToHeadParser()
        self.lineup_parser = LineupParser()
        self.commentary_parser = CommentaryParser()
        self.stats_parser = StatsParser()
        self.standings_parser = StandingsParser()
        self.incidents_parser = IncidentsParser()
        self.odds_parser = OddsParser()
        self.whitelist_manager = whitelist_manager or default_whitelist_manager

    def get_today_matches(self, sport_id: int, apply_whitelist: bool = True) -> List[Match]:
        """Get all matches for today for a specific sport"""
        logger.info(f"Fetching today's matches for sport {sport_id}")

        response_text = self.api.get_matches_for_day(sport_id, day_offset=0)
        if not response_text:
            logger.error("Failed to fetch today's matches")
            return []

        matches = self.match_parser.parse_matches_from_response(response_text, sport_id)
        logger.info(f"Found {len(matches)} matches before filtering")

        if apply_whitelist:
            matches = self._apply_whitelist_filter(matches, sport_id)
            logger.info(f"Found {len(matches)} matches after whitelist filtering")
        else:
            logger.info(f"Found {len(matches)} matches (whitelist disabled)")

        return matches

    def get_matches_for_date_range(self, sport_id: int, days: List[int], apply_whitelist: bool = True) -> List[Match]:
        """Get matches for multiple days"""
        all_matches = []

        for day_offset in days:
            logger.info(f"Fetching matches for day offset {day_offset}")
            response_text = self.api.get_matches_for_day(sport_id, day_offset)

            if response_text:
                matches = self.match_parser.parse_matches_from_response(response_text, sport_id)
                all_matches.extend(matches)
                logger.info(f"Found {len(matches)} matches for day {day_offset}")
            else:
                logger.warning(f"No data for day offset {day_offset}")

        if apply_whitelist and all_matches:
            original_count = len(all_matches)
            all_matches = self._apply_whitelist_filter(all_matches, sport_id)
            logger.info(f"Filtered {original_count} matches to {len(all_matches)} using whitelist")

        return all_matches

    def get_leagues_for_today(self, sport_id: int) -> Dict[str, List[str]]:
        """Get available leagues for today for a specific sport"""
        logger.info(f"Fetching available leagues for sport {sport_id}")

        response_text = self.api.get_matches_for_day(sport_id, day_offset=0)
        if not response_text:
            logger.error("Failed to fetch today's data")
            return {}

        leagues = {}
        try:
            # Split response by league sections
            league_sections = response_text.split('~ZA÷')

            for section in league_sections[1:]:  # Skip first empty section
                try:
                    # Extract league info
                    country_id = int(section.split('ZB÷')[1].split('¬')[0])
                    league_name = section.split('¬')[0]

                    # Count matches in this league
                    match_sections = section.split('~AA÷')
                    match_count = len(match_sections) - 1  # Subtract 1 for the first empty section

                    if league_name not in leagues:
                        leagues[league_name] = []

                    leagues[league_name].append(f"Country ID: {country_id}, Matches: {match_count}")

                except Exception as e:
                    logger.warning(f"Error parsing league section: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error parsing leagues response: {e}")

        logger.info(f"Found {len(leagues)} leagues")
        return leagues

    def _apply_whitelist_filter(self, matches: List[Match], sport_id: int) -> List[Match]:
        """
        Apply whitelist filtering to matches based on league/country criteria

        Args:
            matches: List of matches to filter
            sport_id: Sport ID for sport-specific filtering

        Returns:
            Filtered list of matches
        """
        if not self.whitelist_manager.enabled:
            logger.info("Whitelist filtering is disabled")
            return matches

        filtered_matches = []
        filtered_count = 0

        for match in matches:
            # Check if league is whitelisted using tournament_hash for TOURNAMENT_ID mode
            if (self.whitelist_manager.mode == WhitelistMode.TOURNAMENT_ID and
                hasattr(match, 'tournament_hash') and match.tournament_hash):
                # Use tournament hash for filtering (permanent ID)
                from whitelist import sp_good_ligs
                if match.tournament_hash in sp_good_ligs:
                    filtered_matches.append(match)
                else:
                    filtered_count += 1
                    logger.debug(f"Filtered out: {match.league_name} (Hash: {match.tournament_hash})")
            elif self.whitelist_manager.is_league_whitelisted(
                country_id=match.country_id,
                league_name=match.league_name,
                sport_id=sport_id
            ):
                filtered_matches.append(match)
            else:
                filtered_count += 1
                logger.debug(f"Filtered out: {match.league_name} (Country ID: {match.country_id})")

        logger.info(f"Whitelist filtering: kept {len(filtered_matches)} matches, filtered out {filtered_count}")
        return filtered_matches

    def get_whitelist_stats(self, sport_id: int = 1) -> Dict:
        """Get statistics about current whitelist configuration"""
        return self.whitelist_manager.get_whitelist_stats(sport_id)

    def update_whitelist_mode(self, mode: WhitelistMode) -> None:
        """Update whitelist filtering mode"""
        self.whitelist_manager.mode = mode
        logger.info(f"Updated whitelist mode to: {mode.value}")

    def enable_whitelist(self, enabled: bool = True) -> None:
        """Enable or disable whitelist filtering"""
        self.whitelist_manager.enabled = enabled
        logger.info(f"Whitelist filtering {'enabled' if enabled else 'disabled'}")

    def get_upcoming_matches(self, sport_id: int = 1, days_ahead: int = 7, apply_whitelist: bool = True, only_unstarted: bool = False) -> List[Match]:
        """
        Get upcoming matches for specified number of days ahead

        Args:
            sport_id: Sport ID (1=Football)
            days_ahead: Number of days to look ahead (default: 7)
            apply_whitelist: Whether to apply whitelist filtering
            only_unstarted: Whether to include only matches that haven't started

        Returns:
            List of upcoming matches
        """
        logger.info(f"Fetching upcoming matches for sport {sport_id}, {days_ahead} days ahead")

        # Get matches for multiple days (1 to days_ahead)
        days_range = list(range(1, days_ahead + 1))
        upcoming_matches = self.get_matches_for_date_range(sport_id, days_range, apply_whitelist)

        # Filter matches based on requirements
        filtered_matches = []
        for match in upcoming_matches:
            # Basic filter: exclude finished/cancelled matches
            if match.status in ['FINISHED', 'CANCELLED']:
                continue

            # Additional filter for unstarted matches only
            if only_unstarted:
                # Status codes for started/finished matches
                started_statuses = ['3', '5', '4', '11', '10', '54', '9', '12', '13', '38', '42']
                if match.status in started_statuses:
                    continue

            filtered_matches.append(match)

        logger.info(f"Found {len(filtered_matches)} {'unstarted' if only_unstarted else 'upcoming'} matches")
        return filtered_matches

    def get_match_statistics(self, match: Match) -> Optional[MatchStatistics]:
        """
        Get complete statistics for a match including all tabs data

        Args:
            match: Match object

        Returns:
            MatchStatistics object with all available data or None if failed
        """
        logger.info(f"Fetching statistics for match {match.event_id}: {match.home_team.name} vs {match.away_team.name}")

        try:
            # Initialize all data as None
            h2h_data = None
            betting_odds = None
            lineups = None
            commentary = None
            stats = None
            standings = None
            incidents = None

            # Get H2H data
            h2h_response = self.api.get_head_to_head_data(match.event_id)
            if h2h_response:
                h2h_data = self.h2h_parser.parse_h2h_data(h2h_response, match.home_team.name, match.away_team.name)
                logger.info(f"{'Successfully parsed' if h2h_data else 'Failed to parse'} H2H data")

            # Get betting odds
            odds_response = self.api.get_betting_odds(match.event_id)
            if odds_response:
                betting_odds = self.odds_parser.parse_odds(odds_response)
                logger.info(f"{'Successfully parsed' if betting_odds else 'Failed to parse'} betting odds")

            # Get lineups
            lineups_response = self.api.get_lineups(match.event_id)
            if lineups_response:
                lineups = self.lineup_parser.parse_lineups(lineups_response)
                logger.info(f"{'Successfully parsed' if lineups else 'Failed to parse'} lineups")

            # Get commentary
            commentary_response = self.api.get_match_commentary(match.event_id)
            if commentary_response:
                commentary = self.commentary_parser.parse_commentary(commentary_response)
                logger.info(f"{'Successfully parsed' if commentary else 'Failed to parse'} commentary")

            # Get stats
            stats_response = self.api.get_match_statistics(match.event_id)
            if stats_response:
                stats = self.stats_parser.parse_stats(stats_response)
                logger.info(f"{'Successfully parsed' if stats else 'Failed to parse'} stats")

            # Get standings (requires tournament ID)
            if hasattr(match, 'tournament_hash') and match.tournament_hash:
                standings_response = self.api.get_standings(match.tournament_hash)
                if standings_response:
                    standings = self.standings_parser.parse_standings(standings_response)
                    logger.info(f"{'Successfully parsed' if standings else 'Failed to parse'} standings")

            # Get incidents
            incidents_response = self.api.get_match_incidents(match.event_id)
            if incidents_response:
                incidents = self.incidents_parser.parse_incidents(incidents_response)
                logger.info(f"{'Successfully parsed' if incidents else 'Failed to parse'} incidents")

            return MatchStatistics(
                match=match,
                head_to_head=h2h_data,
                betting_odds=betting_odds,
                lineups=lineups,
                commentary=commentary,
                statistics=stats,
                standings=standings,
                incidents=incidents
            )

        except Exception as e:
            logger.error(f"Error getting statistics for match {match.event_id}: {e}")
            return None

    def save_match_with_statistics(self, match_stats: MatchStatistics, output_dir: str = "matches") -> bool:
        """
        Save individual match with its statistics to JSON file

        Args:
            match_stats: MatchStatistics object
            output_dir: Directory to save files

        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Create output directory if it doesn't exist
            ensure_directory_exists(output_dir)

            # Create filename based on match ID
            filename = f"{match_stats.match.event_id}.json"
            filepath = os.path.join(output_dir, filename)

            # Prepare data for JSON serialization
            match_dict = asdict(match_stats.match)
            match_dict['start_time'] = match_stats.match.start_time.isoformat()

            output_data = {
                "match": match_dict,
                "h2h_data": match_stats.head_to_head,
                "betting_odds": asdict(match_stats.betting_odds) if match_stats.betting_odds else None,
                "lineups": asdict(match_stats.lineups) if match_stats.lineups else None,
                "commentary": asdict(match_stats.commentary) if match_stats.commentary else None,
                "statistics": asdict(match_stats.statistics) if match_stats.statistics else None,
                "standings": asdict(match_stats.standings) if match_stats.standings else None,
                "incidents": asdict(match_stats.incidents) if match_stats.incidents else None,
                "timestamp": datetime.now().isoformat()
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved match statistics to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving match statistics: {e}")
            return False

    def save_matches_to_json(self, matches: List[Match], filename: str,
                           include_whitelist_info: bool = True) -> bool:
        """Save matches to JSON file with optional whitelist information"""
        try:
            # Convert matches to dictionaries for JSON serialization
            matches_data = []
            for match in matches:
                match_dict = asdict(match)
                # Convert datetime to string for JSON serialization
                match_dict['start_time'] = match.start_time.isoformat()
                matches_data.append(match_dict)

            # Create output structure with whitelist information
            output_data = {
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(matches),
                "matches": matches_data
            }

            # Add whitelist information if requested
            if include_whitelist_info:
                output_data["whitelist_info"] = {
                    "enabled": self.whitelist_manager.enabled,
                    "mode": self.whitelist_manager.mode.value,
                    "filtering_applied": self.whitelist_manager.enabled,
                    "stats": self.whitelist_manager.get_whitelist_stats(
                        matches[0].sport_id if matches else 1
                    )
                }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(matches)} matches to {filename}")
            return True

        except Exception as e:
            logger.error(f"Error saving matches to JSON: {e}")
            return False

    def save_matches_to_legacy_format(self, matches: List[Match], filename: str) -> bool:
        """
        Save matches in the original parser's JSON format for backward compatibility

        This method converts the modern Match objects back to the original format
        used by the legacy parser for maximum compatibility.
        """
        try:
            legacy_matches = []

            for match in matches:
                # Convert to original format:
                # [data_matcha, [liga,int(country_id)], team1, team2, '','','',sc1,sc2,'',status, url_game,'',
                #  short_team1, short_team2, '', '', '','','','', sport_id, id_game]

                legacy_match = [
                    match.start_time.strftime('%d.%m.%Y %H:%M'),  # data_matcha
                    [match.league_name, match.country_id],        # [liga, country_id]
                    match.home_team.name,                         # team1
                    match.away_team.name,                         # team2
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    match.home_score,                             # sc1
                    match.away_score,                             # sc2
                    '',                                           # placeholder
                    match.status,                                 # status
                    match.match_url,                              # url_game
                    '',                                           # placeholder
                    match.home_team.short_name,                   # short_team1
                    match.away_team.short_name,                   # short_team2
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    '',                                           # placeholder
                    match.sport_id,                               # sport_id
                    match.event_id                                # id_game
                ]

                legacy_matches.append(legacy_match)

            # Create legacy output structure
            legacy_output = {
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(legacy_matches),
                "format": "legacy_compatible",
                "matches": legacy_matches,
                "whitelist_applied": self.whitelist_manager.enabled
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(legacy_output, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(legacy_matches)} matches to {filename} in legacy format")
            return True

        except Exception as e:
            logger.error(f"Error saving matches to legacy JSON format: {e}")
            return False
