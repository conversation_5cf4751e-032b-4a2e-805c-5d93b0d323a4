"""
Match Parser Module
Handles parsing of match data from FlashScore API responses.
"""

from datetime import datetime
from typing import List, Optional

from data_models import Match, Team
from utils import STATUS_MAPPING, logger


class MatchParser:
    """Parser for match data from FlashScore API responses"""
    
    def __init__(self):
        self.status_mapping = STATUS_MAPPING
    
    def parse_matches_from_response(self, response_text: str, sport_id: int) -> List[Match]:
        """
        Parse matches from API response text
        
        Args:
            response_text: Raw API response
            sport_id: Sport type ID
            
        Returns:
            List of parsed Match objects
        """
        matches = []
        
        try:
            # Split response by league sections
            league_sections = response_text.split('~ZA÷')

            for league_index, section in enumerate(league_sections[1:]):  # Skip first empty section
                try:
                    # Extract league info
                    country_id = int(section.split('ZB÷')[1].split('¬')[0])
                    league_name = section.split('¬')[0]

                    # Extract tournament hash (ZC field)
                    tournament_hash = ""
                    lines = section.split('¬')
                    for line in lines:
                        if line.startswith('ZC÷'):
                            tournament_hash = line[3:]
                            break

                    # Extract individual matches
                    match_sections = section.split('~AA÷')

                    for match_section in match_sections[1:]:  # Skip first section
                        match = self._parse_single_match(
                            match_section, sport_id, league_name, 
                            country_id, league_index, tournament_hash
                        )
                        if match:
                            matches.append(match)

                except Exception as e:
                    logger.warning(f"Error parsing league section: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error parsing matches response: {e}")
            
        return matches
    
    def _parse_single_match(self, match_text: str, sport_id: int, league_name: str, 
                           country_id: int, league_index: int, tournament_hash: str) -> Optional[Match]:
        """Parse a single match from text data"""
        try:
            # Extract basic match info
            event_id = match_text.split('¬')[0]
            status = match_text.split('AC÷')[1].split('¬')[0]
            
            # Parse timestamp
            timestamp = int(match_text.split('AD÷')[1].split('¬')[0])
            start_time = datetime.fromtimestamp(timestamp)
            
            # Extract team names
            home_team_name = match_text.split('AE÷')[1].split('¬')[0]
            away_team_name = match_text.split('AF÷')[1].split('¬')[0]
            
            # Extract short names
            home_short = match_text.split('WM÷')[1].split('¬')[0]
            away_short = match_text.split('WN÷')[1].split('¬')[0]
            
            # Extract scores (if available)
            home_score = ""
            away_score = ""
            try:
                home_score = match_text.split('AG÷')[1].split('¬')[0]
                away_score = match_text.split('AH÷')[1].split('¬')[0]
            except:
                pass
            
            # Create team objects
            home_team = Team(name=home_team_name, short_name=home_short)
            away_team = Team(name=away_team_name, short_name=away_short)
            
            # Create match URL
            match_url = f'https://www.flashscore.com/match/{event_id}/#/match-summary'
            
            # Create match object
            match = Match(
                event_id=event_id,
                start_time=start_time,
                sport_id=sport_id,
                league_name=league_name,
                country_id=country_id,
                country_name="",  # Will be filled later if needed
                home_team=home_team,
                away_team=away_team,
                status=self.status_mapping.get(status, status),
                home_score=home_score,
                away_score=away_score,
                current_result=f"{home_score}:{away_score}" if home_score and away_score else "",
                match_url=match_url,
                league_index=league_index,  # Position of league in API response
                tournament_hash=tournament_hash  # Tournament hash from ZC field
            )
            
            return match
            
        except Exception as e:
            logger.warning(f"Error parsing single match: {e}")
            return None
