#!/usr/bin/env python3
"""
Парсинг матчей ТОЛЬКО из 4 лиг вайтлиста
"""

from flashscore_parser import FlashScoreParser, WhitelistMode
import os
import json
from datetime import datetime

def main():
    print('🎯 Парсинг матчей ТОЛЬКО из 4 лиг вайтлиста')
    print('=' * 60)
    
    try:
        # Создаем парсер
        parser = FlashScoreParser()
        
        # Переключаем на режим TOURNAMENT_ID для использования hash-ов из whitelist.py
        parser.update_whitelist_mode(WhitelistMode.TOURNAMENT_ID)
        parser.enable_whitelist(True)
        print('✅ Вайтлист включен в режиме TOURNAMENT_ID')
        
        # Получаем матчи на 2 дня
        print('\n📅 Получаем матчи на 2 дня...')
        matches = parser.get_matches_for_date_range(
            sport_id=1, 
            days=[1, 2],
            apply_whitelist=True
        )
        
        print(f'📊 Найдено {len(matches)} матчей из вайтлиста')
        
        if matches:
            # Создаем папку
            folder_name = f'whitelist_4_leagues_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            os.makedirs(folder_name, exist_ok=True)
            print(f'📁 Создана папка: {folder_name}')
            
            # Сохраняем каждый матч отдельно
            saved_count = 0
            for match in matches:
                filename = f'{match.event_id}_{match.home_team.short_name}_vs_{match.away_team.short_name}.json'
                filepath = os.path.join(folder_name, filename)
                
                match_data = {
                    'event_id': match.event_id,
                    'start_time': match.start_time.isoformat(),
                    'league_name': match.league_name,
                    'tournament_hash': match.tournament_hash,
                    'home_team': {
                        'name': match.home_team.name,
                        'short_name': match.home_team.short_name
                    },
                    'away_team': {
                        'name': match.away_team.name,
                        'short_name': match.away_team.short_name
                    },
                    'status': match.status,
                    'match_url': match.match_url,
                    'country_id': match.country_id
                }
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(match_data, f, ensure_ascii=False, indent=2)
                
                saved_count += 1
            
            print(f'💾 Сохранено {saved_count} файлов матчей')
            
            # Общий файл
            all_matches_file = os.path.join(folder_name, 'all_matches.json')
            parser.save_matches_to_json(matches, all_matches_file, include_whitelist_info=True)
            print(f'💾 Общий файл: all_matches.json')
            
            # Статистика по лигам
            print(f'\n📈 Статистика по лигам:')
            league_stats = {}
            for match in matches:
                league = match.league_name
                hash_id = match.tournament_hash
                if league not in league_stats:
                    league_stats[league] = {'count': 0, 'hash': hash_id}
                league_stats[league]['count'] += 1
            
            for league, data in league_stats.items():
                print(f'  • {league}: {data["count"]} матчей (hash: {data["hash"]})')
            
            print(f'\n📋 Все матчи:')
            for i, match in enumerate(matches):
                print(f'{i+1}. {match.home_team.name} vs {match.away_team.name}')
                print(f'   {match.league_name}')
                print(f'   {match.start_time.strftime("%d.%m.%Y %H:%M")}')
                print()
                
        else:
            print('❌ Матчи из вайтлиста не найдены')
            
            # Проверим что в вайтлисте
            from whitelist import sp_good_ligs, league_details
            print(f'\n🔍 Проверка вайтлиста:')
            print(f'Hash-и в вайтлисте: {sp_good_ligs}')
            for hash_id, details in league_details.items():
                print(f'  • {hash_id}: {details["name"]}')
        
        print('\n✅ Парсинг завершен')
        
    except Exception as e:
        print(f'❌ Ошибка: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
