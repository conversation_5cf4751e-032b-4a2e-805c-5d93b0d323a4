"""
Statistics Parsers Module
Contains parsers for various match statistics: lineups, commentary, detailed stats, 
standings, incidents, and betting odds.
"""

from typing import Optional, List, Dict

from data_models import (
    Lineup, MatchCommentary, DetailedStats, Standings,
    MatchIncidents, BettingOdds, MatchDetailedStatistics,
    StatisticStage, StatisticGroup, StatisticItem
)
from utils import logger


class LineupParser:
    """Parser for match lineups from FlashScore API"""
    
    def parse_lineups(self, response_text: str) -> Optional[Lineup]:
        """
        Parse lineups data from API response
        
        Args:
            response_text: Raw lineups API response
            
        Returns:
            Lineup object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            # Initialize empty lineups
            home_players = {}
            away_players = {}
            formations = {'home': '', 'away': ''}
            substitutions = []

            # Parse home team lineup (with error checking)
            if '~LA÷' in response_text:
                sections = response_text.split('~LA÷')
                if len(sections) > 1:
                    home_section = sections[1].split('~LB÷')[0] if '~LB÷' in sections[1] else sections[1]
                    for player in home_section.split('¬'):
                        if 'LC÷' in player and 'LD÷' in player:
                            try:
                                player_id = player.split('LC÷')[1].split('¬')[0]
                                player_name = player.split('LD÷')[1].split('¬')[0]
                                home_players[player_id] = player_name
                            except IndexError:
                                continue
            
            # Parse away team lineup (with error checking)
            if '~LB÷' in response_text:
                sections = response_text.split('~LB÷')
                if len(sections) > 1:
                    away_section = sections[1].split('~LC÷')[0] if '~LC÷' in sections[1] else sections[1]
                    for player in away_section.split('¬'):
                        if 'LC÷' in player and 'LD÷' in player:
                            try:
                                player_id = player.split('LC÷')[1].split('¬')[0]
                                player_name = player.split('LD÷')[1].split('¬')[0]
                                away_players[player_id] = player_name
                            except IndexError:
                                continue
            
            # Parse formations (with error checking)
            if '~LE÷' in response_text:
                try:
                    formations['home'] = response_text.split('~LE÷')[1].split('¬')[0]
                except IndexError:
                    pass
            if '~LF÷' in response_text:
                try:
                    formations['away'] = response_text.split('~LF÷')[1].split('¬')[0]
                except IndexError:
                    pass
            
            # Parse substitutions (if any, with error checking)
            if '~LG÷' in response_text:
                subs_section = response_text.split('~LG÷')[1]
                for sub in subs_section.split('¬'):
                    try:
                        if 'LH÷' in sub and 'LI÷' in sub and 'LJ÷' in sub:
                            sub_data = {
                                'team': 'home' if 'LH÷' in sub else 'away',
                                'player_in': sub.split('LH÷')[1].split('¬')[0],
                                'player_out': sub.split('LI÷')[1].split('¬')[0],
                                'minute': sub.split('LJ÷')[1].split('¬')[0]
                            }
                            substitutions.append(sub_data)
                    except IndexError:
                        continue
            
            return Lineup(
                home_team=home_players,
                away_team=away_players,
                formations=formations,
                substitutions=substitutions
            )
            
        except Exception as e:
            logger.error(f"Error parsing lineups: {e}")
            return None


class CommentaryParser:
    """Parser for match commentary from FlashScore API"""
    
    def parse_commentary(self, response_text: str) -> Optional[MatchCommentary]:
        """
        Parse commentary data from API response
        
        Args:
            response_text: Raw commentary API response
            
        Returns:
            MatchCommentary object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            events = []
            timeline = []
            
            # Split into individual events
            event_sections = response_text.split('~MA÷')
            
            for section in event_sections[1:]:  # Skip first empty section
                try:
                    event_type = section.split('MB÷')[1].split('¬')[0]
                    minute = section.split('MC÷')[1].split('¬')[0]
                    text = section.split('MD÷')[1].split('¬')[0]
                    
                    event = {
                        'type': event_type,
                        'minute': minute,
                        'text': text,
                        'is_important': 'ME÷1' in section  # Important events have ME÷1 flag
                    }
                    
                    events.append(event)
                    
                    # Add to timeline if it's a major event
                    if event_type in ['GOAL', 'CARD', 'SUBSTITUTION', 'PENALTY']:
                        timeline.append({
                            'minute': minute,
                            'type': event_type,
                            'text': text
                        })
                        
                except Exception as e:
                    logger.debug(f"Error parsing commentary event: {e}")
                    continue
                    
            return MatchCommentary(
                events=events,
                timeline=timeline
            )
            
        except Exception as e:
            logger.error(f"Error parsing commentary: {e}")
            return None


class StatsParser:
    """Parser for detailed match statistics from FlashScore API"""
    
    def parse_stats(self, response_text: str) -> Optional[DetailedStats]:
        """
        Parse detailed statistics from API response
        
        Args:
            response_text: Raw statistics API response
            
        Returns:
            DetailedStats object or None if parsing failed
        """
        try:
            if not response_text:
                return None
                
            stats = {
                'possession': {'home': 0, 'away': 0},
                'shots': {'home': 0, 'away': 0},
                'shots_on_target': {'home': 0, 'away': 0},
                'corners': {'home': 0, 'away': 0},
                'fouls': {'home': 0, 'away': 0},
                'offsides': {'home': 0, 'away': 0}
            }
            
            # Parse possession
            if '~SA÷' in response_text:
                home_poss = float(response_text.split('~SA÷')[1].split('¬')[0])
                stats['possession'] = {
                    'home': home_poss,
                    'away': 100 - home_poss
                }
            
            # Parse shots
            if '~SB÷' in response_text:
                home_shots = int(response_text.split('~SB÷')[1].split('¬')[0])
                away_shots = int(response_text.split('~SC÷')[1].split('¬')[0])
                stats['shots'] = {
                    'home': home_shots,
                    'away': away_shots
                }
            
            # Parse shots on target
            if '~SD÷' in response_text:
                home_sot = int(response_text.split('~SD÷')[1].split('¬')[0])
                away_sot = int(response_text.split('~SE÷')[1].split('¬')[0])
                stats['shots_on_target'] = {
                    'home': home_sot,
                    'away': away_sot
                }
            
            # Parse corners
            if '~SF÷' in response_text:
                home_corners = int(response_text.split('~SF÷')[1].split('¬')[0])
                away_corners = int(response_text.split('~SG÷')[1].split('¬')[0])
                stats['corners'] = {
                    'home': home_corners,
                    'away': away_corners
                }
            
            # Parse fouls
            if '~SH÷' in response_text:
                home_fouls = int(response_text.split('~SH÷')[1].split('¬')[0])
                away_fouls = int(response_text.split('~SI÷')[1].split('¬')[0])
                stats['fouls'] = {
                    'home': home_fouls,
                    'away': away_fouls
                }
            
            # Parse offsides
            if '~SJ÷' in response_text:
                home_offsides = int(response_text.split('~SJ÷')[1].split('¬')[0])
                away_offsides = int(response_text.split('~SK÷')[1].split('¬')[0])
                stats['offsides'] = {
                    'home': home_offsides,
                    'away': away_offsides
                }
            
            return DetailedStats(
                possession=stats['possession'],
                shots=stats['shots'],
                shots_on_target=stats['shots_on_target'],
                corners=stats['corners'],
                fouls=stats['fouls'],
                offsides=stats['offsides']
            )

        except Exception as e:
            logger.error(f"Error parsing statistics: {e}")
            return None


class StandingsParser:
    """Parser for tournament standings from FlashScore API"""

    def parse_standings(self, response_text: str) -> Optional[Standings]:
        """
        Parse standings data from API response

        Args:
            response_text: Raw standings API response

        Returns:
            Standings object or None if parsing failed
        """
        try:
            if not response_text:
                return None

            teams = []
            groups = []

            # Check if response contains group data
            if '~TA÷' in response_text:
                # Parse group standings
                group_sections = response_text.split('~TA÷')

                for group_section in group_sections[1:]:
                    try:
                        group_name = group_section.split('¬')[0]
                        team_entries = group_section.split('~TB÷')

                        group_teams = []
                        for team_entry in team_entries[1:]:
                            try:
                                team_data = {
                                    'position': team_entry.split('TC÷')[1].split('¬')[0],
                                    'name': team_entry.split('TD÷')[1].split('¬')[0],
                                    'games': team_entry.split('TE÷')[1].split('¬')[0],
                                    'wins': team_entry.split('TF÷')[1].split('¬')[0],
                                    'draws': team_entry.split('TG÷')[1].split('¬')[0],
                                    'losses': team_entry.split('TH÷')[1].split('¬')[0],
                                    'goals_for': team_entry.split('TI÷')[1].split('¬')[0],
                                    'goals_against': team_entry.split('TJ÷')[1].split('¬')[0],
                                    'goal_diff': team_entry.split('TK÷')[1].split('¬')[0],
                                    'points': team_entry.split('TL÷')[1].split('¬')[0]
                                }
                                group_teams.append(team_data)
                            except Exception as e:
                                logger.debug(f"Error parsing team entry: {e}")
                                continue

                        groups.append({
                            'name': group_name,
                            'teams': group_teams
                        })

                    except Exception as e:
                        logger.debug(f"Error parsing group section: {e}")
                        continue
            else:
                # Parse single table standings
                team_entries = response_text.split('~TB÷')

                for team_entry in team_entries[1:]:
                    try:
                        team_data = {
                            'position': team_entry.split('TC÷')[1].split('¬')[0],
                            'name': team_entry.split('TD÷')[1].split('¬')[0],
                            'games': team_entry.split('TE÷')[1].split('¬')[0],
                            'wins': team_entry.split('TF÷')[1].split('¬')[0],
                            'draws': team_entry.split('TG÷')[1].split('¬')[0],
                            'losses': team_entry.split('TH÷')[1].split('¬')[0],
                            'goals_for': team_entry.split('TI÷')[1].split('¬')[0],
                            'goals_against': team_entry.split('TJ÷')[1].split('¬')[0],
                            'goal_diff': team_entry.split('TK÷')[1].split('¬')[0],
                            'points': team_entry.split('TL÷')[1].split('¬')[0]
                        }
                        teams.append(team_data)
                    except Exception as e:
                        logger.debug(f"Error parsing team entry: {e}")
                        continue

            return Standings(
                teams=teams,
                groups=groups
            )

        except Exception as e:
            logger.error(f"Error parsing standings: {e}")
            return None


class IncidentsParser:
    """Parser for match incidents (goals, cards, subs) from FlashScore API"""

    def parse_incidents(self, response_text: str) -> Optional[MatchIncidents]:
        """
        Parse match incidents from API response

        Args:
            response_text: Raw incidents API response

        Returns:
            MatchIncidents object or None if parsing failed
        """
        try:
            if not response_text:
                return None

            goals = []
            cards = []
            substitutions = []

            # Split into individual incidents
            incident_sections = response_text.split('~IA÷')

            for section in incident_sections[1:]:  # Skip first empty section
                try:
                    incident_type = section.split('IB÷')[1].split('¬')[0]
                    minute = section.split('IC÷')[1].split('¬')[0]

                    if incident_type == 'GOAL':
                        # Parse goal
                        goal_data = {
                            'minute': minute,
                            'team': section.split('ID÷')[1].split('¬')[0],
                            'player': section.split('IE÷')[1].split('¬')[0],
                            'type': section.split('IF÷')[1].split('¬')[0] if 'IF÷' in section else '',
                            'score': section.split('IG÷')[1].split('¬')[0] if 'IG÷' in section else ''
                        }
                        goals.append(goal_data)

                    elif incident_type == 'CARD':
                        # Parse card
                        card_data = {
                            'minute': minute,
                            'team': section.split('ID÷')[1].split('¬')[0],
                            'player': section.split('IE÷')[1].split('¬')[0],
                            'color': section.split('IF÷')[1].split('¬')[0],
                            'reason': section.split('IG÷')[1].split('¬')[0] if 'IG÷' in section else ''
                        }
                        cards.append(card_data)

                    elif incident_type == 'SUBSTITUTION':
                        # Parse substitution
                        sub_data = {
                            'minute': minute,
                            'team': section.split('ID÷')[1].split('¬')[0],
                            'player_in': section.split('IE÷')[1].split('¬')[0],
                            'player_out': section.split('IF÷')[1].split('¬')[0]
                        }
                        substitutions.append(sub_data)

                except Exception as e:
                    logger.debug(f"Error parsing incident: {e}")
                    continue

            return MatchIncidents(
                goals=goals,
                cards=cards,
                substitutions=substitutions
            )

        except Exception as e:
            logger.error(f"Error parsing incidents: {e}")
            return None


class OddsParser:
    """Parser for betting odds from FlashScore API"""

    def parse_odds(self, response_text: str) -> Optional[BettingOdds]:
        """
        Parse betting odds from API response

        Args:
            response_text: Raw odds API response

        Returns:
            BettingOdds object or None if parsing failed
        """
        try:
            if not response_text:
                return None

            odds = BettingOdds()

            # Parse main odds (1X2)
            if '~OA÷' in response_text:
                odds.home_win = float(response_text.split('~OA÷')[1].split('¬')[0])
                odds.draw = float(response_text.split('~OB÷')[1].split('¬')[0])
                odds.away_win = float(response_text.split('~OC÷')[1].split('¬')[0])

            # Parse over/under odds
            if '~OD÷' in response_text:
                odds.over_under_line = response_text.split('~OD÷')[1].split('¬')[0]
                odds.over_odds = float(response_text.split('~OE÷')[1].split('¬')[0])
                odds.under_odds = float(response_text.split('~OF÷')[1].split('¬')[0])

            # Parse both teams to score odds
            if '~OG÷' in response_text:
                odds.both_teams_score_yes = float(response_text.split('~OG÷')[1].split('¬')[0])
                odds.both_teams_score_no = float(response_text.split('~OH÷')[1].split('¬')[0])

            return odds

        except Exception as e:
            logger.error(f"Error parsing odds: {e}")
            return None


class DetailedStatisticsParser:
    """Parser for detailed match statistics in FlashScore format"""

    def parse_detailed_statistics(self, response_text: str) -> Optional[MatchDetailedStatistics]:
        """
        Parse detailed statistics from API response into FlashScore format

        Args:
            response_text: Raw statistics API response

        Returns:
            MatchDetailedStatistics object or None if parsing failed
        """
        try:
            if not response_text:
                return None

            stages = []

            # Parse different stages (Match, 1st Half, 2nd Half)
            stage_sections = response_text.split('~ST÷')

            for stage_section in stage_sections[1:]:  # Skip first empty section
                try:
                    stage_name = self._extract_stage_name(stage_section)
                    groups = self._parse_statistic_groups(stage_section)

                    if groups:
                        stage = StatisticStage(
                            stage_name=stage_name,
                            groups=groups
                        )
                        stages.append(stage)

                except Exception as e:
                    logger.debug(f"Error parsing stage section: {e}")
                    continue

            # If no stages found, try to parse as single match statistics
            if not stages:
                groups = self._parse_statistic_groups(response_text)
                if groups:
                    stage = StatisticStage(
                        stage_name="Match",
                        groups=groups
                    )
                    stages.append(stage)

            return MatchDetailedStatistics(
                data=stages,
                last_change_key=None
            )

        except Exception as e:
            logger.error(f"Error parsing detailed statistics: {e}")
            return None

    def _extract_stage_name(self, section_text: str) -> str:
        """Extract stage name from section"""
        try:
            if '~SN÷' in section_text:
                return section_text.split('~SN÷')[1].split('¬')[0]
            return "Match"
        except:
            return "Match"

    def _parse_statistic_groups(self, section_text: str) -> List[StatisticGroup]:
        """Parse statistic groups from section"""
        groups = []

        try:
            # Parse different groups (Top stats, Shots, Attack, etc.)
            group_sections = section_text.split('~SG÷')

            for group_section in group_sections[1:]:  # Skip first section
                try:
                    group_label = self._extract_group_label(group_section)
                    items = self._parse_statistic_items(group_section)

                    if items:
                        group = StatisticGroup(
                            group_label=group_label,
                            items=items
                        )
                        groups.append(group)

                except Exception as e:
                    logger.debug(f"Error parsing group section: {e}")
                    continue

            # If no groups found, create default groups based on known statistics
            if not groups:
                groups = self._create_default_groups(section_text)

        except Exception as e:
            logger.warning(f"Error parsing statistic groups: {e}")

        return groups

    def _extract_group_label(self, section_text: str) -> str:
        """Extract group label from section"""
        try:
            if '~GL÷' in section_text:
                return section_text.split('~GL÷')[1].split('¬')[0]
            return "Statistics"
        except:
            return "Statistics"

    def _parse_statistic_items(self, section_text: str) -> List[StatisticItem]:
        """Parse individual statistic items from section"""
        items = []

        try:
            # Parse individual statistics
            stat_sections = section_text.split('~SI÷')

            for stat_section in stat_sections[1:]:  # Skip first section
                try:
                    incident_name = self._extract_incident_name(stat_section)
                    value_home = self._extract_home_value(stat_section)
                    value_away = self._extract_away_value(stat_section)

                    if incident_name:
                        item = StatisticItem(
                            incident_name=incident_name,
                            value_home=value_home,
                            value_away=value_away
                        )
                        items.append(item)

                except Exception as e:
                    logger.debug(f"Error parsing statistic item: {e}")
                    continue

        except Exception as e:
            logger.warning(f"Error parsing statistic items: {e}")

        return items

    def _extract_incident_name(self, section_text: str) -> str:
        """Extract incident name from section"""
        try:
            if '~IN÷' in section_text:
                return section_text.split('~IN÷')[1].split('¬')[0]
            return ""
        except:
            return ""

    def _extract_home_value(self, section_text: str) -> str:
        """Extract home team value from section"""
        try:
            if '~VH÷' in section_text:
                return section_text.split('~VH÷')[1].split('¬')[0]
            return ""
        except:
            return ""

    def _extract_away_value(self, section_text: str) -> str:
        """Extract away team value from section"""
        try:
            if '~VA÷' in section_text:
                return section_text.split('~VA÷')[1].split('¬')[0]
            return ""
        except:
            return ""
