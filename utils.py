"""
Utility functions and common helpers for FlashScore Parser
Contains logging setup, common mappings, and helper functions.
"""

import logging
import os
from typing import Dict, Any


# Configure logging
def setup_logging():
    """Setup logging configuration for the parser"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('flashscore_parser.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


# Common status mappings
STATUS_MAPPING = {
    '3': 'FINISHED',
    '5': 'CANCELLED', 
    '4': 'POSTPONED',
    '11': 'AFTER_PENALTIES',
    '10': 'AFTER_EXTRA_TIME',
    '54': 'TECHNICAL_DEFEAT',
    '9': 'NO_SHOW',
    '12': 'FIRST_HALF',
    '13': 'SECOND_HALF',
    '38': 'HALF_TIME',
    '42': 'AWAITING_UPDATES'
}

# H2H result mappings
H2H_RESULT_MAPPING = {
    'l': 'LOST',
    'w': 'WIN',
    'd': 'DRAW',
    'lo': 'LOST',
    'wo': 'WIN'
}


def safe_extract(entry: str, field_code: str, default: str = "") -> str:
    """
    Safely extract field from API response entry
    
    Args:
        entry: Raw API response entry
        field_code: Field code to extract (e.g., 'KC÷')
        default: Default value if extraction fails
        
    Returns:
        Extracted value or default
    """
    try:
        if field_code in entry:
            return entry.split(field_code)[1].split('¬')[0]
        return default
    except (IndexError, ValueError):
        return default


def safe_extract_int(entry: str, field_code: str, default: int = 0) -> int:
    """
    Safely extract integer field from API response entry
    
    Args:
        entry: Raw API response entry
        field_code: Field code to extract (e.g., 'KC÷')
        default: Default value if extraction fails
        
    Returns:
        Extracted integer value or default
    """
    try:
        if field_code in entry:
            return int(entry.split(field_code)[1].split('¬')[0])
        return default
    except (IndexError, ValueError):
        return default


def safe_extract_float(entry: str, field_code: str, default: float = 0.0) -> float:
    """
    Safely extract float field from API response entry
    
    Args:
        entry: Raw API response entry
        field_code: Field code to extract
        default: Default value if extraction fails
        
    Returns:
        Extracted float value or default
    """
    try:
        if field_code in entry:
            return float(entry.split(field_code)[1].split('¬')[0])
        return default
    except (IndexError, ValueError):
        return default


def create_image_url(image_code: str) -> str:
    """
    Create full image URL from image code
    
    Args:
        image_code: Image code from API response
        
    Returns:
        Full image URL or empty string if no code
    """
    if image_code:
        return f"https://www.flashscore.com/res/image/data/{image_code}"
    return ""


def get_team_abbreviation(team_name: str, length: int = 3) -> str:
    """
    Get team abbreviation from full team name
    
    Args:
        team_name: Full team name
        length: Length of abbreviation (default: 3)
        
    Returns:
        Team abbreviation in uppercase
    """
    if not team_name:
        return ""
    
    # Remove common words and get abbreviation
    words = team_name.replace("FC", "").replace("AC", "").replace("SC", "").strip().split()
    if len(words) == 1:
        return words[0][:length].upper()
    elif len(words) >= 2:
        # Take first letter of each word
        abbr = "".join([word[0] for word in words if word])
        return abbr[:length].upper()
    else:
        return team_name[:length].upper()


def ensure_directory_exists(directory_path: str) -> None:
    """
    Ensure directory exists, create if it doesn't
    
    Args:
        directory_path: Path to directory
    """
    os.makedirs(directory_path, exist_ok=True)


def validate_match_data(match_data: Dict[str, Any]) -> bool:
    """
    Validate essential match data fields
    
    Args:
        match_data: Dictionary containing match data
        
    Returns:
        True if valid, False otherwise
    """
    required_fields = ['EVENT_ID', 'START_TIME']
    return all(field in match_data and match_data[field] for field in required_fields)


# Initialize logger
logger = setup_logging()
